//
//  IMYServerRequestSessionDelegate.m
//  IMYPublic
//
//  Created by <PERSON><PERSON><PERSON> on 2025/09/05.
//

#import "IMYServerRequestSessionDelegate.h"
#import "IMYNetworkingTaskMetrics.h"
#import "NSError+IMYNetworking.h"
#import <objc/runtime.h>

static const void *IMYTaskMetricsKey = &IMYTaskMetricsKey;

@implementation IMYServerRequestSessionDelegate

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics API_AVAILABLE(ios(10.0)) {
    IMYNetworkingTaskMetrics *taskMetrics = [[IMYNetworkingTaskMetrics alloc] initWithTaskMetrics:metrics];
    objc_setAssociatedObject(task, IMYTaskMetricsKey, taskMetrics, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(nullable NSError *)error {
    if (error) {
        id<IMYNetworkingTaskMetricsProtocol> taskMetrics = objc_getAssociatedObject(task, IMYTaskMetricsKey);
        if (taskMetrics) {
            [error setImy_taskMetrics:taskMetrics];
        }
    }
}

@end
