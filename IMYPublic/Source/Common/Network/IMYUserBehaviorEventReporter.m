//
//  IMYUserBehaviorEventReporter.m
//  IMYPublic
//
//  Created by AI Assistant on 2025/01/09.
//  Copyright © 2025 meiyou. All rights reserved.
//

#import "IMYUserBehaviorEventReporter.h"
#import "IMYPublicServerRequest.h"
#import "IMYPublicURL.h"
#import <ReactiveCocoa/ReactiveCocoa.h>

@implementation IMYUserBehaviorEventReporter

+ (void)reportEvent:(IMYUserBehaviorEventType)eventType 
         completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion {
    [self reportEvent:eventType appId:1 completion:completion];
}

+ (void)reportEvent:(IMYUserBehaviorEventType)eventType 
              appId:(NSInteger)appId
         completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion {
    
    // 参数验证
    if (eventType != IMYUserBehaviorEventTypeColdStart && 
        eventType != IMYUserBehaviorEventTypeHotStart) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"IMYUserBehaviorEventReporter" 
                                                 code:-1001 
                                             userInfo:@{NSLocalizedDescriptionKey: @"无效的事件类型"}];
            completion(NO, error);
        }
        return;
    }
    
    // 构建请求参数
    NSTimeInterval currentTimestamp = [[NSDate date] timeIntervalSince1970] * 1000; // 转换为13位毫秒时间戳
    NSDictionary *params = @{
        @"event": @(eventType),
        @"unix_timestamp": @((NSInteger)currentTimestamp),
        @"app_id": @(appId)
    };
    
    // 发送请求
    [[IMYPublicServerRequest postPath:@"/event/track" 
                                 host:mgo_seeyouyima_com 
                               params:params 
                              headers:nil] 
     subscribeNext:^(id<IMYHTTPResponse> response) {
        // 请求成功
        if (completion) {
            completion(YES, nil);
        }
    } error:^(NSError *error) {
        // 请求失败
        if (completion) {
            completion(NO, error);
        }
    }];
}

@end
