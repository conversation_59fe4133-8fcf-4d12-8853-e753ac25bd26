//
//  IMYUserBehaviorEventReporter.h
//  IMYPublic
//
//  Created by AI Assistant on 2025/01/09.
//  Copyright © 2025 meiyou. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 用户行为事件类型
 */
typedef NS_ENUM(NSInteger, IMYUserBehaviorEventType) {
    IMYUserBehaviorEventTypeColdStart = 1,  ///< 冷启动：从未在内存的状态启动 App
    IMYUserBehaviorEventTypeHotStart = 2    ///< 热启动：App 从后台回到前台，且距离上次前台已超过设定的会话超时阈值
};

/**
 * 用户行为事件上报器
 * 用于向服务器上报用户的行为事件，如冷启动、热启动等
 */
@interface IMYUserBehaviorEventReporter : NSObject

/**
 * 上报用户行为事件（使用默认应用ID）
 * @param eventType 事件类型，支持冷启动和热启动
 * @param completion 完成回调，success表示是否成功，error表示错误信息（成功时为nil）
 */
+ (void)reportEvent:(IMYUserBehaviorEventType)eventType 
         completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;

/**
 * 上报用户行为事件（指定应用ID）
 * @param eventType 事件类型，支持冷启动和热启动
 * @param appId 应用ID，默认为1（美柚APP）
 * @param completion 完成回调，success表示是否成功，error表示错误信息（成功时为nil）
 */
+ (void)reportEvent:(IMYUserBehaviorEventType)eventType 
              appId:(NSInteger)appId
         completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;

@end

NS_ASSUME_NONNULL_END
