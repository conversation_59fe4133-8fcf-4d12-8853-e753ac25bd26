//
//  IMYServerRequest.m
//  IMYPublic
//
//  Created by mario on 16/4/9.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import "IMYServerRequest.h"
#import "IMYMeetyouBuildable.h"
#import "IMYMeetyouHTTPHooks.h"
#import "IMYPublicURL.h"
#import "IMYCacheHelper.h"
#import "IMYHTTPBackupHostConfig.h"
#import <IMYFoundation/IMYFoundation.h>

@implementation IMYServerRequest

#pragma mark - RACSignal

+ (RACSignal *)headPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self head:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)putPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self put:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)patchPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self patch:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)deletePath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self delete:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)getPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self get:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)getPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers progress:(ProgressCallback)progress {
    return [self get:path host:host params:params headers:headers progress:progress].signal;
}

+ (RACSignal *)postPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self post:path host:host params:params headers:headers].signal;
}

+ (RACSignal *)postPath:(NSString *)path host:(NSString *)host querys:(id)querys params:(id)params headers:(id)headers {
    IMYHTTPBuildable *buildable = [self post:path host:host params:params headers:headers];
    buildable.URLQueries(querys);
    return buildable.signal;
}

+ (RACSignal *)postPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers formdata:(FormDataCallback)formdata serializerType:(IMYHTTPSerializerType)serializerType {
    return [self post:path host:host params:params headers:headers formdata:formdata serializerType:serializerType].signal;
}

+ (RACSignal *)postPath:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers formdata:(FormDataCallback)formdata serializerType:(IMYHTTPSerializerType)serializerType progress:(ProgressCallback)progress {
    return [self post:path host:host params:params headers:headers formdata:formdata serializerType:serializerType progress:progress].signal;
}

+ (RACSignal *)downloadPath:(NSString *)fileUrl params:(id)params headers:(id)headers progress:(ProgressCallback)progress {
    return [self download:fileUrl params:params headers:headers progress:progress].signal;
}

+ (RACDisposable *)dataTaskWithRequest:(NSURLRequest *)request completionHandler:(void (^)(NSData *, NSURLResponse *, NSError *))completionHandler {
    IMYMeetyouBuildable *buildable = (IMYMeetyouBuildable *)[self buildable];
    buildable.headersHook = nil;
    buildable.acceptableContentTypesHook = nil;
    buildable.taskRequest(request);
    buildable.ResponseSerializerType(IMYHTTPSerializerTypeData);
    buildable.Priority(IMYHTTPTaskPriorityLow);
    return [buildable.signal subscribeNext:^(IMYHTTPResponse *response) {
        NSURLResponse *httpResponse = [response response];
        NSData *data = [response responseData];
        if (completionHandler) {
            completionHandler(data, httpResponse, nil);
        }
    } error:^(NSError *error) {
        NSURLResponse *httpResponse = [error af_httpResponse];
        NSData *data = [error af_responseData];
        if (completionHandler) {
            completionHandler(data, httpResponse, error);
        }
    }];
}

// 支持断点续传的下载API
+ (void)downloadAndResumeTaskWithURL:(NSURL *)url
                   completionHandler:(void (^)(NSURL *, NSURLResponse *, NSError *))completionHandler {
    [self _downloadAndResumeTaskWithURL:url
                              beginTime:CFAbsoluteTimeGetCurrent()
                             retryCount:0
                      completionHandler:completionHandler];
}

+ (void)_downloadAndResumeTaskWithURL:(NSURL * const)url
                            beginTime:(uint64_t const)beginTime
                           retryCount:(uint64_t const)retryCount
                    completionHandler:(void (^const)(NSURL *, NSURLResponse *, NSError *))completionHandler {
    // 断点续传在所有系统上（iOS10-iOS14）都有bug，所以不开启断点续传，只保留内部重试
    void (^resumeCompletionHandler)(NSURL *, NSURLResponse *, NSError *) = ^(NSURL *location, NSURLResponse *response, NSError *error) {
        if ([response isKindOfClass:NSHTTPURLResponse.class] && !error) {
            const NSInteger statusCode = [(NSHTTPURLResponse *)response statusCode];
            const BOOL validCode = (statusCode >= 200 && statusCode < 300 && statusCode != 203);
            if (!validCode) { // 无效的 HTTP Code
                error = [NSError errorWithDomain:@"http code invalid!" code:statusCode userInfo:nil];
                location = nil;
            }
        }
        if (error != nil && error.code < NSURLErrorBadURL && IMYNetState.networkEnable) {
            // 因为网络原因的下载失败，15秒内会重试3次
            uint64_t timeDiff = CFAbsoluteTimeGetCurrent() - beginTime;
            if (timeDiff < 15 && retryCount < 2) {
                [self _downloadAndResumeTaskWithURL:url
                                          beginTime:beginTime
                                         retryCount:retryCount + 1
                                  completionHandler:completionHandler];
                return;
            }
        }
        // 回调外部
        completionHandler(location, response, error);
    };
    NSURLSessionTask *task = [[NSURLSession sharedSession] downloadTaskWithURL:url completionHandler:resumeCompletionHandler];
    [task resume];
}

+ (RACDisposable *)downloadTaskWithURL:(NSURL *)url completionHandler:(void (^)(NSURL *, NSURLResponse *, NSError *))completionHandler {
    if (!completionHandler || !url) {
        NSAssert(NO, @"IMYServerRequest downloadTaskWithURL params invalid!");
        return nil;
    }
    // 先进行域名替换逻辑
    NSURL *downloadURL = [[IMYPublicURLManager sharedManager] switchHostWithURL:url];
    // 寻找备用域名配置
    IMYHTTPBackupHostConfig *config = [IMYHTTPBackupHostConfig getBackupHostConfigWithHost:downloadURL.host];
    // 没有备用域名配置 或者 备用域名也异常了，直接走 no catch 模式
    if (!config || config.backupFail) {
        // 无备用域名的下载流程
        [self downloadAndResumeTaskWithURL:downloadURL
                         completionHandler:completionHandler];
    } else if (config.useBackup) {
        // 使用域名进行请求，并且记录备用域名出错次数，超过阈值 则切换回原域名
        NSURL *backupURL = [downloadURL imy_setHost:config.backup];
        [self downloadAndResumeTaskWithURL:backupURL
                         completionHandler:^(NSURL *location, NSURLResponse *response, NSError *error) {
            if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                config.backupFail = YES;
            }
            completionHandler(location, response, error);
        }];
    } else {
        // 走默认逻辑，先使用原始域名请求，当出现网络层的连接错误后， 使用备用域名请求
        [self downloadAndResumeTaskWithURL:downloadURL
                         completionHandler:^(NSURL *location, NSURLResponse *response, NSError *error) {
            if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                // 修改全局配置，其他地方也使用备用域名请求
                config.useBackup = YES;
                // 修改域名，自动重试
                NSURL *backupURL = [downloadURL imy_setHost:config.backup];
                [self downloadAndResumeTaskWithURL:backupURL
                                 completionHandler:^(NSURL *location, NSURLResponse *response, NSError *error) {
                    if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                        config.backupFail = YES;
                    }
                    completionHandler(location, response, error);
                }];
            } else {
                // 正常返回值，回调外部
                completionHandler(location, response, error);
            }
        }];
    }
    return nil;
}
+ (NSURLSessionUploadTask *)uploadTaskWithRequest:(NSURLRequest *)request fromFile:(NSURL *)fileURL completionHandler:(void (^)(NSData *, NSURLResponse *, NSError *))completionHandler {
    if (!completionHandler || !request || !fileURL) {
        NSAssert(NO, @"uploadTaskWithRequest params invalid!");
        return nil;
    }
    // 先进行域名替换逻辑
    NSURL *uploadURL = [[IMYPublicURLManager sharedManager] switchHostWithURL:request.URL];
    if (request.URL != uploadURL) {
        NSMutableURLRequest *backupRequest = [request imy_mutableCopyWorkaround];
        backupRequest.URL = uploadURL;
        request = backupRequest;
    }
    // 寻找备用域名配置
    IMYHTTPBackupHostConfig *config = [IMYHTTPBackupHostConfig getBackupHostConfigWithHost:uploadURL.host];
    // 没有备用域名配置 或者 备用域名也异常了，直接走 no catch 模式
    if (!config || config.backupFail) {
        NSURLSessionTask *task = [[self sharedCustomSession] uploadTaskWithRequest:request fromFile:fileURL completionHandler:completionHandler];
        [task resume];
    } else if (config.useBackup) {
        // 使用域名进行请求，并且记录备用域名出错次数，超过阈值 则切换回原域名
        NSURL *backupURL = [uploadURL imy_setHost:config.backup];
        NSMutableURLRequest *backupRequest = [request imy_mutableCopyWorkaround];
        backupRequest.URL = backupURL;
        NSURLSessionTask *backupTask = [[self sharedCustomSession] uploadTaskWithRequest:backupRequest fromFile:fileURL completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
           if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                config.backupFail = YES;
            }
            completionHandler(data, response, error);
        }];
        [backupTask resume];
    } else {
        // 走默认逻辑，先使用原始域名请求，当出现网络层的连接错误后， 使用备用域名请求
        NSURLSessionTask *task = [[self sharedCustomSession] uploadTaskWithRequest:request fromFile:fileURL completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
            if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                // 修改全局配置，其他地方也使用备用域名请求
                config.useBackup = YES;
                // 修改域名，自动重试
                NSURL *backupURL = [uploadURL imy_setHost:config.backup];
                NSMutableURLRequest *backupRequest = [request imy_mutableCopyWorkaround];
                backupRequest.URL = backupURL;
                NSURLSessionTask *backupTask = [[self sharedCustomSession] uploadTaskWithRequest:backupRequest fromFile:fileURL completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                    if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
                        config.backupFail = YES;
                    }
                    completionHandler(data, response, error);
                }];
                [backupTask resume];
            } else {
                // 正常返回值，回调外部
                completionHandler(data, response, error);
            }
        }];
        [task resume];
    }
    return nil;
}

#pragma mark - Buildable

+ (IMYHTTPBuildable *)buildable {
    return [IMYMeetyouBuildable new];
}

+ (IMYHTTPBuildable *)head:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodHead path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *)put:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodPut path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *)patch:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodPatch path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *) delete:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodDelete path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *)get:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodGet path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *)get:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers progress:(ProgressCallback)progress {
    IMYHTTPBuildable *buildable = [self buildable];
    return buildable.Host(host).Path(path).Parameters(params).Headers(headers).Method(HTTPMethodGet).Progress(progress);
}

+ (IMYHTTPBuildable *)post:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    return [self method:HTTPMethodPost path:path host:host params:params headers:headers];
}

+ (IMYHTTPBuildable *)post:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers formdata:(FormDataCallback)formdata serializerType:(IMYHTTPSerializerType)serializerType {
    IMYHTTPBuildable *buildable = [self buildable];
    return buildable.Host(host).Path(path).Parameters(params).Headers(headers).Method(HTTPMethodPost).FormData(formdata).RequestSerializerType(serializerType);
}

+ (IMYHTTPBuildable *)post:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers formdata:(FormDataCallback)formdata serializerType:(IMYHTTPSerializerType)serializerType progress:(ProgressCallback)progress {
    IMYHTTPBuildable *buildable = [self buildable];
    return buildable.Host(host).Path(path).Parameters(params).Headers(headers).Method(HTTPMethodPost).FormData(formdata).RequestSerializerType(serializerType).Progress(progress);
}

+ (IMYHTTPBuildable *)download:(NSString *)fileUrl params:(id)params headers:(id)headers progress:(ProgressCallback)progress {
    IMYHTTPBuildable *buildable = [self method:HTTPMethodGet path:fileUrl host:nil params:params headers:headers];
    return buildable.Progress(progress).ResponseSerializerType(IMYHTTPSerializerTypeData);
}

+ (IMYHTTPBuildable *)method:(HTTPMethod)method path:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    IMYHTTPBuildable *buildable = [self buildable];
    return buildable.Host(host).Path(path).Parameters(params).Headers(headers).Method(method);
}

@end
YHTTPBuildable *buildable = [self method:HTTPMethodGet path:fileUrl host:nil params:params headers:headers];
    return buildable.Progress(progress).ResponseSerializerType(IMYHTTPSerializerTypeData);
}

+ (IMYHTTPBuildable *)method:(HTTPMethod)method path:(NSString *)path host:(NSString *)host params:(id)params headers:(id)headers {
    IMYHTTPBuildable *buildable = [self buildable];
    return buildable.Host(host).Path(path).Parameters(params).Headers(headers).Method(method);
}

@end
