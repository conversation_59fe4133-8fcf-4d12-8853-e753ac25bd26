platform :ios,'12.0'

install! 'cocoapods', :disable_input_output_paths => true, :deterministic_uuids => false
inhibit_all_warnings!

workspace 'Seeyou'
project 'Seeyou/Seeyou'
target 'Seeyou' do
    # Repo 依赖， 1.7 之后可以用CDN地址：
    source 'https://cdn.cocoapods.org/'
    # 头条的源，大社区引入字节流需求需要
    source 'https://github.com/volcengine/volcengine-specs.git'
    #百川源
    source 'https://gitlab.meiyou.com/Github-iOS/taobao-baichuansdk-alibcspecs.git'
    #私有库 repo 源
    source 'https://gitlab.meiyou.com/iOS/imyspecs.git'

    # 指定 :podspec 的方式，会根据版本号判断，避免每次都更新私有 repo 的问题
    # :podspec，会先用HTTP直接拉取对应文件，比 git pull 速度快
    # 如果用 :git 的方式 每次更新都会执行一次 git pull 耗时较大

    # 引入本地库pod, 在另外一个文件独立维护
    eval(File.read(File.join(__dir__, 'Podfile.lib')), binding)
 
if !$ZZIMYMain
    # 主工程
    pod 'ZZIMYMain', :podspec => 'https://gitlab.meiyou.com/iOS/ZZIMYMain/raw/release-8.96.0/ZZIMYMain.podspec'
end

if !$IMYBaseKit
    #底层模块
    pod 'IMYBaseKit', :podspec => 'https://gitlab.meiyou.com/iOS/IMYBaseKit/raw/release-8.96.0/IMYBaseKit.podspec'
end
    
if !$IMYCommonKit
    # 业务公共层
    pod 'IMYCommonKit', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCommonKit/raw/release-8.96.0/IMYCommonKit.podspec'
end

    # 美柚第三方Keys
    pod 'IMYKeyManager/MeetYou', :podspec => 'https://gitlab.meiyou.com/iOS/IMYKeyManager/raw/release-8.83.0/IMYKeyManager.podspec'

    # 全局协议层 IMYHiveMind
    pod 'IOC-Protocols', :podspec => 'https://gitlab.meiyou.com/iOS/IOC-Protocols/raw/master/IOC-Protocols.podspec'
    
    # 基础模块
    pod 'IMYTCP', :podspec => 'https://gitlab.meiyou.com/iOS/IMYTCP/raw/release-jingqi-8.60.0/IMYTCP.podspec'
    pod 'IMYLaunchController', :podspec => 'https://gitlab.meiyou.com/iOS/IMYLaunchController/raw/release-8.95.0/IMYLaunchController.podspec'
    pod 'IMYReactNative', :podspec => 'https://gitlab.meiyou.com/iOS/IMYReactNative/raw/0.64.2/IMYReactNative.podspec'
    pod 'IMYCCAPI', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCCAPI/raw/master/IMYCCAPI.podspec'
    pod 'IMYMWPhotoBrowser', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMWPhotoBrowser/raw/release-1.2.0/IMYMWPhotoBrowser.podspec'
    
if !$IMYFeedback
    pod 'IMYFeedback', :podspec => 'https://gitlab.meiyou.com/iOS/IMYFeedback/raw/release-8.95.0/IMYFeedback.podspec'
end
    
    #SWift库
    pod 'SnapKit'
    pod 'SwiftyJSON', '5.0.2'
    pod 'RxSwift'
    pod 'RxCocoa'
    
    # IMY Swift基础库
if !$IMYSwift
    pod 'IMYSwift', :podspec => 'https://gitlab.meiyou.com/iOS/IMYSwift/raw/release-1.2.0/IMYSwift.podspec'
end
    # Chat AI
if !$CHATAI
    pod 'ChatAI', :podspec => 'https://gitlab.meiyou.com/iOS/ChatAI/raw/release-8.96.0/ChatAI.podspec'
end
    pod 'IMYMarkdown', :podspec => 'https://gitlab.meiyou.com/iOS/apple-swift-markdown/raw/release-8.94.0/IMYMarkdown.podspec'
    # 富文本组件, 8.92.0 html富文本需求引入
    pod 'IMYCoreText', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCoreText/-/raw/release-1.0.0/IMYCoreText.podspec', :modular_headers => true
    
    # 视频
    pod 'LLVideoPlayer', :podspec => 'https://gitlab.meiyou.com/iOS/LLVideoPlayer/raw/master/LLVideoPlayer.podspec'
    pod 'IMYVideoPlayer', :podspec => 'https://gitlab.meiyou.com/iOS/IMYVideoPlayer/raw/release-8.60.0/IMYVideoPlayer.podspec'
    # 社区
#    pod 'QuCore_RCE', :podspec => 'https://gitlab.meiyou.com/iOS/QuCore_RCE/raw/feature/Qucore_2_Meishe/QuCore_RCE.podspec'
    pod 'IMYMPN', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMPN/raw/release-8.68.0/IMYMPN.podspec'
    pod 'IMYSVR', :podspec => 'https://gitlab.meiyou.com/iOS/IMYSVR/raw/release-8.92.0/IMYSVR.podspec'

if !$IMYTTQ
    pod 'IMYTTQ', :podspec => 'https://gitlab.meiyou.com/iOS/IMYTTQ/raw/release-8.96.0/IMYTTQ.podspec'
end

if !$IMYNews
    # 资讯
    pod 'IMYNews', :podspec => 'https://gitlab.meiyou.com/iOS/IMYNews/raw/release-8.96.0/IMYNews.podspec'
end
    # 新知识首页模块
    pod 'IMYKnowledge', :podspec => 'https://gitlab.meiyou.com/iOS/IMYKnowledge/raw/release-8.63.0/IMYKnowledge.podspec'

if !$IMYUGC
    # 大社区业务
    pod 'IMYUGC', :podspec => 'https://gitlab.meiyou.com/iOS/IMYUGC/raw/release-8.96.0/IMYUGC.podspec'
end

if !$IMYAnswer
    # 问答
    pod 'IMYAnswer', :podspec => 'https://gitlab.meiyou.com/iOS/IMYAnswer/raw/release-8.96.0/IMYAnswer.podspec'
end
    # 电商模块
    # 电商百川库 
    pod 'TaeSDK/MeetYou', :podspec => 'https://gitlab.meiyou.com/iOS/TaeSDK/raw/feature/tae_sdk_5.0.0.18_alipay_15.8.32/TaeSDK.podspec'
   
    # 电商公共库
    pod 'IMYEBPublic',:podspec => 'https://gitlab.meiyou.com/iOS/IMYEBPublic/raw/public_1.9.1/IMYEBPublic.podspec'
    pod 'IMYEBViewKit', :podspec => 'https://gitlab.meiyou.com/iOS/IMYEBViewKit/raw/dev_1.8.5/IMYEBViewKit.podspec'
    # 电商搜索库
    pod 'IMYEBSearch', :podspec => 'https://gitlab.meiyou.com/iOS/IMYEBSearch/raw/search_1.8.6/IMYEBSearch.podspec'
    # 电商业务库
    pod 'IMY_EBusiness', :podspec => 'https://gitlab.meiyou.com/iOS/IMY_EBusiness/raw/2025/release-1.9.1/IMY_EBusiness_JQ.podspec' 
    #电商直播库
    pod 'IMYEBLiveBroadcast',:podspec=>'https://gitlab.meiyou.com/iOS/IMYEBLiveBroadcast/raw/live_1.7.5/IMYEBLiveBroadcast_JQ.podspec'
    
    #返还电商库
    pod 'IMYFHBusiness', :podspec => 'https://gitlab.meiyou.com/iOS/IMYFHBusiness/raw/release-jingqi-8.96.0/IMYFHBusiness.podspec'
    pod 'IMYFHPublic', :podspec => 'https://gitlab.meiyou.com/iOS/IMYFHPublic/raw/jingqi/release-8.95.0/IMYFHPublic.podspec'
   
    #优品模块
    pod 'IMYYoupin', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYoupin/raw/release-8.62.0/IMYYoupin.podspec'
     
    #播放器SDK
    pod 'AliPlayerSDK', :podspec => 'https://gitlab.meiyou.com/iOS/AliPlayerSDK/raw/5.3.2/AliPlayerSDK.podspec', :configurations => ['Release']
    pod 'AliPlayerSDKDebug', :podspec => 'https://gitlab.meiyou.com/iOS/AliPlayerSDK/raw/5.3.2_simulator_no_bitcode/AliPlayerSDK.podspec', :configurations => ['Debug']
    #京东SDK
    pod 'JD-SDK',:podspec=>'https://gitlab.meiyou.com/iOS/JD-SDK/raw/JDSDK_20240812/JD-SDK-JQ.podspec'
    #拼多多SDK
    pod 'PDDSDK/MeetYou', :podspec => 'https://gitlab.meiyou.com/Github-iOS/PDDSDK/raw/PDDSDK-2025.1.2/PDDSDK.podspec'
    
    # 经期记录 [release]
if !$IMYRecord
    pod 'IMYRecord', :podspec => 'https://gitlab.meiyou.com/iOS/IMYRecord/raw/release-8.96.0/IMYRecord.podspec'
end

    # 孕期
    #孕育模块首页823+
    pod 'IMYYunyuHome', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYunyuHome/raw/release-8.96.0/IMYYunyuHome.podspec'
    # 孕育变化相关（宝宝变化、妈妈变化）
    pod 'IMYYunyuChange', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYunyuChange/raw/release-8.92.0/IMYYunyuChange.podspec'
    pod 'IMYYQBasicServices', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYQBasicServices/raw/release-8.96.0/IMYYQBasicServices.podspec'
    pod 'IMYYQHome', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYQHome/raw/release-8.95.0/IMYYQHome.podspec'
    pod 'IMYTools', :podspec => 'https://gitlab.meiyou.com/iOS/IMY_Tools/raw/release-jingqi-8.96.0/IMYTools.podspec'
    pod 'IMYLamaHome', :podspec => 'https://gitlab.meiyou.com/Youbaobao/IMYLamaHome/raw/release-8.92.0/IMYLamaHome.podspec'
    pod 'IMYTools_Swift', :podspec => 'https://gitlab.meiyou.com/Youbaobao/IMYTools_Swift/raw/release-8.94.0/IMYTools_Swift.podspec'
    pod 'IMYYunyuReport', :podspec => 'https://gitlab.meiyou.com/iOS/IMYYunyuReport/raw/release-8.95.0/IMYYunyuReport.podspec'
    pod 'IMYPostpartumRecovery', :podspec => 'https://gitlab.meiyou.com/iOS/IMYPostpartumRecovery/raw/release-8.96.0/IMYPostpartumRecovery.podspec'

    # 在家早教模块 - swift
    pod 'IMYECE', :podspec => 'https://gitlab.meiyou.com/iOS/IMYECE/raw/release-8.95.0/IMYECE.podspec'
    
    pod 'YBBTools', :podspec => 'https://gitlab.meiyou.com/Youbaobao/YBBTools/raw/release-8.93.0/YBBTools.podspec'
    # 辣妈「生命1000天」
    pod 'IMYThousandDays', :podspec => 'https://gitlab.meiyou.com/iOS/IMYThousandDays/raw/release-8.94.0/IMYThousandDays.podspec'
    # 早教助手
    pod 'IMYEduAide', :podspec => 'https://gitlab.meiyou.com/Youbaobao/IMYEduAide/raw/release-8.93.0/IMYEduAide.podspec'
    # 喂养记录
    pod 'IMYBabyFeed', :podspec => 'https://gitlab.meiyou.com/iOS/IMYBabyFeed/raw/release-8.96.0/IMYBabyFeed.podspec'
    # 产检时间表
    pod 'IMYGravidityCheck', :podspec => 'https://gitlab.meiyou.com/iOS/IMYGravidityCheck/raw/release-8.95.0/IMYGravidityCheck.podspec'
    pod 'SeeyouWidgetService', :podspec => 'https://gitlab.meiyou.com/iOS/SeeyouWidgetService/raw/release-8.96.0/SeeyouWidgetService.podspec'
    # 宝宝记相册
    pod 'TensorFlowLite', :podspec => 'https://gitlab.meiyou.com/iOS/TensorFlowLite/raw/master/TensorFlowLite.podspec.json' #智能识别库
#    pod 'BBJTensorFlowModel', :podspec => 'https://gitlab.meiyou.com/iOS/BBJTensorFlowModel/raw/feature/6.4.0/BBJTensorFlowModel.podspec'#智能识别模型
    pod 'BBJPhotoRecognition', :podspec => 'https://gitlab.meiyou.com/iOS/BBJPhotoRecognition/raw/release-8.78.0/BBJPhotoRecognition.podspec'

    pod 'BBJBabyHome', :podspec => 'https://gitlab.meiyou.com/iOS/BBJBabyHome/raw/release-lama-8.96.0/BBJBabyHome.podspec'
    pod 'BBJViewKit', :podspec => 'https://gitlab.meiyou.com/iOS/BBJViewKit/raw/release-lama-8.96.0/BBJViewKit.podspec'
    # 排卵试纸 NCNN
    pod 'IMYNCNNFrameworks', :podspec => 'https://gitlab.meiyou.com/iOS/IMYNCNNFrameworks/raw/release-8.63.0/IMYNCNNFrameworks.podspec'
    
if !$MeAccount
    # 用户模块
    pod 'IMYMe', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMe/raw/release-8.96.0/IMYMe.podspec'
    # 账号模块
    pod 'IMYAccount', :podspec => 'https://gitlab.meiyou.com/iOS/IMYAccount/raw/release-jingqi-8.91.0/IMYAccount.podspec'
end

    # 广告
    pod 'IMYAdvertisement', :podspec => 'https://gitlab.meiyou.com/iOS/IMYAdvertisement/raw/jingqi/release-8.96.0/IMYAdvertisement.podspec'

    #穿山甲 广告
    pod 'Ads-CN', '*******', :subspecs => ['BUAdSDK', 'BUAdLive']
    pod 'TTSDKFramework', '********-premium', :subspecs => ['LivePull-Lite'], :source => 'https://github.com/volcengine/volcengine-specs'

    #快手sdk广告
    pod 'KSAdSDK','********'

    # 巨量引擎SDK
    pod 'BDASignalSDK', :podspec => 'https://gitlab.meiyou.com/Github-iOS/BDASignalSDK/raw/master/BDASignalSDK.podspec'
    
    # 小工具
    pod 'IMYMiniProgram', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMiniProgram/raw/release-8.88.0/IMYMiniProgram.podspec'

    # 医美
    # pod 'IMYCosmetology', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCosmetology/raw/dev/IMYCosmetology.podspec'

    # 消息
if !$IMYMSG
    pod 'IMYMSG', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMSG/raw/release-jingqi-8.96.0/IMYMSG.podspec'
end
    pod 'IMYMSGJump', :podspec => 'https://gitlab.meiyou.com/iOS/IMYMSGJump/raw/release-8.60.0/IMYMSGJump.podspec'

    # 客户端统计 APM
    pod 'Poseidon', :podspec => 'https://gitlab.meiyou.com/iOS/Poseidon/raw/release-8.60.0/Poseidon.podspec'
    pod 'HarryAPM', :podspec => 'https://gitlab.meiyou.com/iOS/HarryAPM/raw/release-8.64.0/HarryAPM.podspec'

    # DEBUG调试用
    pod 'IMYDEBUG', :podspec => 'https://gitlab.meiyou.com/iOS/IMYDEBUG/raw/master-library/IMYDEBUG.podspec', :configurations => ['Debug']
    pod 'LookinServer', :podspec => 'https://gitlab.meiyou.com/Github-iOS/LookinServer/raw/master/LookinServer.podspec', :configurations => ['Debug'] # 方便查看 CI 包的 UI 层级
    pod 'IMYDebugHooks/Meetyou', :podspec => 'https://gitlab.meiyou.com/iOS/IMYDebugHooks/-/raw/release-8.73.0/IMYDebugHooks.podspec', :configurations => ['Debug']
    
    # 第三方库
    #设定版本的好处 就是 pod update 的时候 不会重新下载代码
    pod 'INTULocationManager', '4.3.2'

    # 第三方魔改过的代码
    pod 'MXParallaxHeader', :podspec => 'https://gitlab.meiyou.com/iOS/MXParallaxHeader/raw/master-meetyou/MXParallaxHeader.podspec'
    pod 'HPGrowingTextView', :podspec => 'https://gitlab.meiyou.com/iOS/HPGrowingTextView/raw/dev/HPGrowingTextView.podspec'
    pod 'MJRefresh', :podspec => 'https://gitlab.meiyou.com/Github-iOS/MJRefresh/raw/master/MJRefresh.podspec'

    pod 'BlocksKit', :podspec => 'https://gitlab.meiyou.com/Github-iOS/BlocksKit/raw/source/BlocksKit.podspec'
    pod 'CHTCollectionViewWaterfallLayout', :podspec => 'https://gitlab.meiyou.com/Github-iOS/CHTCollectionViewWaterfallLayout/raw/master/CHTCollectionViewWaterfallLayout.podspec'
    pod 'ReactiveCocoa', :podspec => 'https://gitlab.meiyou.com/Github-iOS/ReactiveCocoa/raw/source/ReactiveCocoa.podspec'
    pod 'StandardPaths', :podspec => 'https://gitlab.meiyou.com/Github-iOS/StandardPaths/raw/master/StandardPaths.podspec'
    pod 'CocoaSecurity',  :podspec => 'https://gitlab.meiyou.com/Github-iOS/CocoaSecurity/raw/master/CocoaSecurity.podspec'

    pod 'YYModel', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYModel/raw/master/YYModel.podspec'
    pod 'YYImage', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYImage/raw/source/YYImage.podspec'
    pod 'YYCache', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYCache/raw/master/YYCache.podspec'
    pod 'YYText', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYText/raw/master/YYText.podspec'

    pod 'LKDBHelper', :podspec => 'https://gitlab.meiyou.com/Github-iOS/LKDBHelper/raw/master/LKDBHelper.podspec'
    pod 'FMDB', :podspec => 'https://gitlab.meiyou.com/Github-iOS/FMDB/raw/master/FMDB.podspec'
    pod 'LKDBRecover', :podspec => 'https://gitlab.meiyou.com/Github-iOS/LKDBRecover/raw/master/LKDBRecover.podspec'

    # 网络库
    pod 'AFNetworking', :podspec => 'https://gitlab.meiyou.com/Github-iOS/AFNetworking/raw/source/AFNetworking.podspec'

    # 图片加载框架
    pod 'libwebp', :podspec => 'https://gitlab.meiyou.com/Github-iOS/libwebp/raw/master/libwebp.podspec'
    pod 'SDWebImage', :podspec => 'https://gitlab.meiyou.com/Github-iOS/SDWebImage/raw/source/SDWebImage.podspec'
    pod 'FLAnimatedImage', :podspec => 'https://gitlab.meiyou.com/Github-iOS/FLAnimatedImage/-/raw/source/FLAnimatedImage.podspec'
    pod 'libpag-enterprise', :podspec => 'https://gitlab.meiyou.com/Github-iOS/libpag/raw/release-movie-4.4.24/libpag-enterprise.podspec'
    # React-Native
    # pod 'React', '0.59.0' 
    # pod 'yoga','0.59.0'
    # pod 'Folly','2018.10.22.00'
    # pod 'glog','0.3.5'
    # pod 'DoubleConversion','1.1.6'

    pod 'React', :podspec => 'https://gitlab.meiyou.com/Github-iOS/React/raw/0.64.1/React.podspec', :configurations => 'Release'
    pod 'React-Debug', :podspec => 'https://gitlab.meiyou.com/Github-iOS/React-Debug/raw/0.64.1/React-Debug.podspec', :configurations => 'Debug'
    pod 'yoga', :podspec => 'https://gitlab.meiyou.com/Github-iOS/yoga/raw/0.64.1/yoga.podspec'
    pod 'DoubleConversion', :podspec => 'https://gitlab.meiyou.com/Github-iOS/DoubleConversion/raw/0.64.1/DoubleConversion.podspec'
    pod 'glog', :podspec => 'https://gitlab.meiyou.com/Github-iOS/glog/raw/0.64.1/glog.podspec'
    pod 'hermes-engine', :podspec => 'https://gitlab.meiyou.com/Github-iOS/hermes-engine/raw/0.64.1/hermes-engine.podspec'
    pod 'libevent', :podspec => 'https://gitlab.meiyou.com/Github-iOS/libevent/raw/0.64.1/libevent.podspec'

    pod 'CommonCrypto', :podspec => 'https://gitlab.meiyou.com/iOS/CommonCrypto/raw/master/CommonCrypto.podspec'
    pod 'DACircularProgress', :podspec => 'https://gitlab.meiyou.com/iOS/DACircularProgress/raw/master/DACircularProgress.podspec'


    # 物理引擎
    # pod 'PHYKit-iOS', :podspec => 'https://gitlab.meiyou.com/Github-iOS/PHYKit-iOS/raw/master/PHYKit-iOS.podspec'

    # 动画库
    pod 'lottie-ios', :podspec => 'https://gitlab.meiyou.com/Github-iOS/lottie-ios/raw/source/lottie-ios.podspec'
    pod 'pop', :podspec => 'https://gitlab.meiyou.com/Github-iOS/pop/raw/master/pop.podspec'

    # 动态库合集
    pod 'IMYDynamicFrameworks/Meetyou', :podspec => 'https://gitlab.meiyou.com/Github-iOS/IMYDynamicFrameworks/raw/release-8.96.0/IMYDynamicFrameworks.podspec'

    # 七鱼SDK，优品要求加入
    pod 'QY_iOS_SDK', :podspec => 'https://gitlab.meiyou.com/iOS/QY_iOS_SDK/raw/10.3.0/QY_iOS_SDK.podspec'

    # 蓝牙库
    pod 'BabyBluetooth', :podspec => 'https://gitlab.meiyou.com/Github-iOS/BabyBluetooth/-/raw/master/BabyBluetooth.podspec'
    
    # 根据CODE_COVERAGE_ENABLED环境变量开关值决定是否加载IMYCodeCoverageKit库
    if env_true?('CODE_COVERAGE_ENABLED')
      pod 'IMYCodeCoverageKit', :podspec => 'https://gitlab.meiyou.com/iOS/IMYCodeCoverageKit/raw/1.0.0/IMYCodeCoverageKit.podspec', :configurations => ['Debug']
    end
end

target 'Seeyou Today' do
    # Repo 依赖， 1.7 之后可以用CDN地址：source 'https://cdn.jsdelivr.net/cocoa/'
    source 'https://cdn.cocoapods.org/'
    pod 'Masonry'
    pod 'YYModel', :podspec => 'https://gitlab.meiyou.com/Github-iOS/YYModel/raw/master/YYModel.podspec'
end

target 'SeeyouWidgetExtension' do
    platform :ios,'12.0'
    # Repo 依赖， 1.7 之后可以用CDN地址：source 'https://cdn.jsdelivr.net/cocoa/'
    source 'https://cdn.cocoapods.org/'
    pod 'Alamofire', '5.8.1'
    pod 'SwiftyJSON', '5.0.2'
    pod 'SeeyouWidget', :podspec => 'https://gitlab.meiyou.com/iOS/SeeyouWidget/raw/release-8.96.0/SeeyouWidget.podspec'
    pod 'SeeyouWidgetService', :podspec => 'https://gitlab.meiyou.com/iOS/SeeyouWidgetService/raw/release-8.96.0/SeeyouWidgetService.podspec'
    # pod 'Kingfisher', '7.10.2'
end

target 'SeeyouWatch Watch App' do
    platform :watchos, '10.0'
    # Repo 依赖， 1.7 之后可以用CDN地址：source 'https://cdn.jsdelivr.net/cocoa/'
    source 'https://cdn.cocoapods.org/'
    # pod 'Alamofire', '5.8.1'
    # pod 'Kingfisher', '7.10.2'
    # pod 'IMYWatchApp',:path =>"../Tmp/IMYWatchApp"
    pod 'IMYWatchApp', :podspec => 'https://gitlab.meiyou.com/iOS/IMYWatchApp/raw/release-8.83.0/IMYWatchApp.podspec'
end


target 'SeeyouIntents' do
    platform :ios,'12.0'
    # Repo 依赖， 1.7 之后可以用CDN地址：source 'https://cdn.jsdelivr.net/cocoa/'
    source 'https://cdn.cocoapods.org/'
    pod 'Alamofire', '5.8.1'
    pod 'SwiftyJSON', '5.0.2'
    pod 'SeeyouWidgetService', :podspec => 'https://gitlab.meiyou.com/iOS/SeeyouWidgetService/raw/release-8.96.0/SeeyouWidgetService.podspec'
    # pod 'Kingfisher', '7.10.2'
end


# 二进制排序
# pod 'AppOrderFiles'
# post_install do |installer|
#   installer.pods_project.targets.each do |target|
#     target.build_configurations.each do |config|
#           cflags = config.build_settings['OTHER_CFLAGS'] || ['$(inherited)']
#           cflags << '-fsanitize-coverage=func,trace-pc-guard'
#           config.build_settings['OTHER_CFLAGS'] = cflags

#           swiftflags = config.build_settings['OTHER_SWIFT_FLAGS'] || ['$(inherited)']
#           swiftflags << '-sanitize-coverage=func -sanitize=undefined'
#           config.build_settings['OTHER_SWIFT_FLAGS'] = swiftflags
#     end
#   end
# end


if env_true?('CODE_COVERAGE_ENABLED')
  # 执行插桩参数配置
  eval(File.read(File.join(__dir__, 'PodfileCodeCoverInstallScripts.rb')), binding)
else
  # 执行原有的podinstall配置
  eval(File.read(File.join(__dir__, 'PodfileNormalInstallScripts.rb')), binding)
end
# ----------------------
