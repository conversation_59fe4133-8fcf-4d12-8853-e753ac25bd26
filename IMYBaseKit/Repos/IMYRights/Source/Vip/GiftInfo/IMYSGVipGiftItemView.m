//
//  IMYSGVipGiftItemView.m
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import "IMYSGVipGiftItemView.h"
#import "IMYBaseKit.h"
#import "IMYSGVipGiftExplainDialog.h"

@interface IMYSGVipGiftItemView () <IMYTimerRuningProtocol>

/// 图标
@property (nonatomic, strong) UIImageView *iconView;

/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 价格
@property (nonatomic, strong) UILabel *priceLabel;

/// 倒计时、标签
@property (nonatomic, strong) UILabel *tagLabel;
/// 倒计时前缀
@property (nonatomic, strong) UILabel *preTagLabel;

/// 选中按钮
@property (nonatomic, strong) UIImageView *selectView;

/// 说明按钮
@property (nonatomic, strong) UIImageView *explainView;

@end

@implementation IMYSGVipGiftItemView

- (void)setGiftItem:(IMYSubGuidePricingGiftInfoListItem * const)giftItem {
    _giftItem = giftItem;
    // 构建UI
    [self setupUI];
}

- (void)setupUI {
    if (self.cardWidth <= 0) {
        NSAssert(NO, @"还未设置卡片宽度!");
        self.cardWidth = SCREEN_WIDTH - 32 - 32;
    }
    // 先确定卡片大小
    if (self.cardStyle == 1) {
        // 单赠礼样式
        if (self.giftItem.icon.length > 0) {
            // 有头像
            self.imy_size = CGSizeMake(self.cardWidth, 64);
            [self setupWithStyle1];
        } else {
            // 无头像
            self.imy_size = CGSizeMake(self.cardWidth, 44);
            [self setupWithStyle2];
        }
    } else if (self.cardStyle == 2) {
        // 双赠礼样式
        self.imy_size = CGSizeMake(self.cardWidth, 56);
        [self setupWithStyle3];
    } else if (self.cardStyle == 3) {
        // 多赠礼样式
        self.imy_size = CGSizeMake(self.cardWidth, 56);
        [self setupWithStyle3];
    } else {
        self.imy_size = CGSizeMake(self.cardWidth, 56);
        [self setupWithStyle3];
        NSAssert(NO, @"无效的参数!");
    }
    
    // 卡片背景色+圆角
    [self imy_setBackgroundColor:kCK_White_AN];
    [self imy_drawAllCornerRadius:8];
    
    // 刷新数据
    [self refreshData];
}

- (void)setIsSelected:(NSInteger const)isSelected {
    _isSelected = isSelected;
    if (isSelected == 0) {
        // 不采用懒加载，直接隐藏
        _selectView.hidden = YES;
    } else if (isSelected == 1) {
        self.selectView.hidden = NO;
        self.selectView.image = [UIImage imageNamed:@"icon_gift_select_actived"];
    } else if (isSelected == 2) {
        self.selectView.hidden = NO;
        self.selectView.image = [UIImage imageNamed:@"icon_gift_select_default"];
    } else {
        _selectView.hidden = YES;
        NSAssert(NO, @"无效的参数!");
    }
}

- (void)refreshData {
    // 标题
    self.titleLabel.text = self.giftItem.name;
    
    // 头像
    if (self.giftItem.icon.length > 0) {
        [self.iconView imy_setOriginalImageURL:self.giftItem.icon];
    }
    
    // 价格
    if (self.giftItem.show_price || self.giftItem.show_worth) {
        NSString *priceText = nil;
        NSString *worthText = nil;
        NSString *allText = @"";
        if (self.giftItem.show_price) {
            priceText = @"¥0";
            allText = [allText stringByAppendingString:priceText];
        }
        if (self.giftItem.show_worth) {
            worthText = [NSString stringWithFormat:@"¥%@", self.giftItem.worth];
            allText = [allText stringByAppendingString:worthText];
        }
        NSInteger cardStyle = self.cardStyle;
        [self.priceLabel imy_addThemeChangedBlock:^(UILabel *weakObject) {
            NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:allText];
            // 价格的字体样式
            if (priceText.length > 0) {
                [attrs addAttributes:@{
                    NSFontAttributeName: [UIFont systemFontOfSize:11 weight:UIFontWeightRegular],
                    NSForegroundColorAttributeName: IMY_COLOR_KEY(kCK_Black_A),
                } range:NSMakeRange(0, 1)];
                
                if (cardStyle == 1) {
                    [attrs addAttributes:@{
                        NSFontAttributeName: [UIFont systemFontOfSize:21 weight:UIFontWeightMedium],
                        NSForegroundColorAttributeName: IMY_COLOR_KEY(kCK_Black_A),
                    } range:NSMakeRange(1, 1)];
                } else {
                    [attrs addAttributes:@{
                        NSFontAttributeName: [UIFont systemFontOfSize:15 weight:UIFontWeightMedium],
                        NSForegroundColorAttributeName: IMY_COLOR_KEY(kCK_Black_A),
                    } range:NSMakeRange(1, 1)];
                }
            }
            // 划线价的字体样式
            if (worthText.length > 0) {
                [attrs addAttributes:@{
                    NSFontAttributeName: [UIFont systemFontOfSize:11 weight:UIFontWeightRegular],
                    NSForegroundColorAttributeName: IMY_COLOR_KEY(kCK_Black_B),
                    NSStrikethroughStyleAttributeName: @(NSUnderlineStyleSingle),
                } range:NSMakeRange(priceText.length, allText.length - priceText.length)];
            }
            weakObject.attributedText = attrs;
        }];
    }
    
    // 倒计时
    if (self.giftItem.hasCountdown) {
        [self imy_timerRuning];
        [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    }
}

/// 单赠礼有头像
- (void)setupWithStyle1 {
    // 名称最长宽度
    NSInteger titleMaxWidth = self.cardWidth - 12 - 12;
    
    // 头像
    self.iconView.hidden = NO;
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@38);
        make.leading.equalTo(@12);
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    // 扣除 头像+间距
    titleMaxWidth -= (38 + 8);
    
    // 是否显示价格文案
    if (self.giftItem.show_price || self.giftItem.show_worth) {
        self.priceLabel.hidden = NO;
        
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@15);
            make.leading.equalTo(self.iconView.mas_trailing).offset(8);
            make.top.equalTo(@11);
        }];
        
        [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(6);
            make.leading.equalTo(self.iconView.mas_trailing).offset(8);
            make.height.equalTo(@21);
        }];
    } else {
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@15);
            make.leading.equalTo(self.iconView.mas_trailing).offset(8);
            make.centerY.equalTo(self.iconView.mas_centerY);
        }];
    }
    
    // 有说明按钮
    if (self.giftItem.desc.length > 0 || self.giftItem.desc_uri.length > 0) {
        self.explainView.hidden = NO;
        [self.explainView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.titleLabel.mas_centerY);
            make.leading.equalTo(self.titleLabel.mas_trailing).offset(2);
            make.width.height.equalTo(@12);
        }];
        
        // 扣除 说明按钮+间距
        titleMaxWidth -= (12 + 2);
    }
    
    // 有倒计时
    if (self.giftItem.hasCountdown) {
        self.preTagLabel.hidden = NO;
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
        self.tagLabel.text = @"00:00:00";
        [self.tagLabel imy_sizeToFit];
        
        NSInteger const tagWidth = self.tagLabel.imy_width + 8;
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-12);
            make.centerY.equalTo(self.mas_centerY);
            make.height.equalTo(@18);
            make.width.equalTo(@(tagWidth));
        }];
        
        [self.preTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.tagLabel.mas_leading).offset(-2);
            make.centerY.equalTo(self.tagLabel.mas_centerY);
        }];
        
        // 扣除 倒计时+间距+前缀(22pt)+间隙
        titleMaxWidth -= (tagWidth + 2 + 22 + 8);
    } else if (self.giftItem.tag.length > 0) {
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        self.tagLabel.text = self.giftItem.tag;
        [self.tagLabel imy_sizeToFit];
        
        NSInteger const tagWidth = self.tagLabel.imy_width + 8;
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-12);
            make.centerY.equalTo(self.mas_centerY);
            make.height.equalTo(@18);
            make.width.equalTo(@(self.tagLabel.imy_width + 8));
        }];
        
        // 扣除 标签+间距
        titleMaxWidth -= (tagWidth + 8);
    }
    
    // 设置标题最长宽度
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
       make.width.lessThanOrEqualTo(@(titleMaxWidth));
    }];
}

/// 单赠礼无头像
- (void)setupWithStyle2 {
    // 名称最长宽度
    NSInteger titleMaxWidth = self.cardWidth - 12 - 12;
    
    // 标题
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@15);
        make.leading.equalTo(@12);
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    // 有说明按钮
    if (self.giftItem.desc.length > 0 || self.giftItem.desc_uri.length > 0) {
        self.explainView.hidden = NO;
        [self.explainView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.titleLabel.mas_centerY);
            make.leading.equalTo(self.titleLabel.mas_trailing).offset(2);
            make.width.height.equalTo(@12);
        }];
        
        // 扣除 说明按钮+间距
        titleMaxWidth -= (12 + 2);
    }
    
    // 有倒计时
    if (self.giftItem.hasCountdown) {
        self.preTagLabel.hidden = NO;
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
        self.tagLabel.text = @"00:00:00";
        [self.tagLabel imy_sizeToFit];
        
        NSInteger const tagWidth = self.tagLabel.imy_width + 8;
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-12);
            make.centerY.equalTo(self.mas_centerY);
            make.height.equalTo(@18);
            make.width.equalTo(@(tagWidth));
        }];
        
        [self.preTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.tagLabel.mas_leading).offset(-2);
            make.centerY.equalTo(self.tagLabel.mas_centerY);
        }];
        
        // 扣除 倒计时+间距+前缀(22pt)+间隙
        titleMaxWidth -= (tagWidth + 2 + 22 + 8);
    } else if (self.giftItem.tag.length > 0) {
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        self.tagLabel.text = self.giftItem.tag;
        [self.tagLabel imy_sizeToFit];
        
        NSInteger const tagWidth = self.tagLabel.imy_width + 8;
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-12);
            make.centerY.equalTo(self.mas_centerY);
            make.height.equalTo(@18);
            make.width.equalTo(@(tagWidth));
        }];
        
        // 扣除 标签+间距
        titleMaxWidth -= (tagWidth + 8);
    }
    
    // 是否显示价格文案
    if (self.giftItem.show_price || self.giftItem.show_worth) {
        self.priceLabel.hidden = NO;
        
        [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.height.equalTo(@21);
            if (!self.preTagLabel.hidden) {
                make.trailing.equalTo(self.preTagLabel.mas_leading).offset(-8);
            } else if (!self.tagLabel.hidden) {
                make.trailing.equalTo(self.tagLabel.mas_leading).offset(-8);
            } else {
                make.trailing.equalTo(self.mas_trailing).offset(-12);
            }
        }];
        
        // 扣除 价格（42pt）+ 间距(8pt)
        if (self.giftItem.show_price && self.giftItem.show_worth) {
            titleMaxWidth -= (42 + 8);
        } else {
            titleMaxWidth -= (21 + 8);
        }
    }
    
    // 设置标题最长宽度
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
       make.width.lessThanOrEqualTo(@(titleMaxWidth));
    }];
}

/// 多个赠礼
- (void)setupWithStyle3 {
    // 名称最长宽度
    NSInteger titleMaxWidth = self.cardWidth - 6 - 6;
    
    // 2行样式
    BOOL const isMultiStyle = (self.giftItem.show_price || self.giftItem.show_worth || self.giftItem.hasCountdown || self.giftItem.tag.length > 0);
    
    // 头像
    if (self.giftItem.icon.length > 0) {
        self.iconView.hidden = NO;
        self.iconView.layer.cornerRadius = 6;
        [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.equalTo(@20);
            make.leading.equalTo(@6);
            if (isMultiStyle) {
                make.top.equalTo(@6);
            } else {
                make.centerY.equalTo(self.mas_centerY);
            }
        }];
        
        // 扣除 头像+间距
        titleMaxWidth -= (20 + 4);
    }
    
    // 需要拆分为多行
    self.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@14);
        if (!self.iconView.hidden) {
            make.leading.equalTo(self.iconView.mas_trailing).offset(4);
            make.centerY.equalTo(self.iconView.mas_centerY);
        } else {
            make.leading.equalTo(@6);
            if (isMultiStyle) {
                make.top.equalTo(@9);
            } else {
                make.centerY.equalTo(self.mas_centerY);
            }
        }
    }];
    
    // 有说明按钮
    if (self.giftItem.desc.length > 0 || self.giftItem.desc_uri.length > 0) {
        self.explainView.hidden = NO;
        [self.explainView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.titleLabel.mas_centerY);
            make.leading.equalTo(self.titleLabel.mas_trailing).offset(2);
            make.width.height.equalTo(@12);
        }];
        
        // 扣除 说明按钮+间距
        titleMaxWidth -= (12 + 2);
    }
    
    // 是否显示价格文案
    if (self.giftItem.show_price || self.giftItem.show_worth) {
        self.priceLabel.hidden = NO;
        
        [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(9);
            make.leading.equalTo(@6);
            make.height.equalTo(@17);
        }];
    }
    
    // 有倒计时
    if (self.giftItem.hasCountdown) {
        self.preTagLabel.hidden = NO;
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
        self.tagLabel.text = @"00:00:00";
        [self.tagLabel imy_sizeToFit];
        
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-6);
            make.top.equalTo(self.titleLabel.mas_bottom).offset(9);
            make.height.equalTo(@18);
            make.width.equalTo(@(self.tagLabel.imy_width + 8));
        }];
        
        [self.preTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.tagLabel.mas_leading).offset(-2);
            make.centerY.equalTo(self.tagLabel.mas_centerY);
        }];
    } else if (self.giftItem.tag.length > 0) {
        self.tagLabel.hidden = NO;
        
        self.tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        self.tagLabel.text = self.giftItem.tag;
        [self.tagLabel imy_sizeToFit];
        
        [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-6);
            make.top.equalTo(self.titleLabel.mas_bottom).offset(9);
            make.height.equalTo(@18);
            make.width.equalTo(@(self.tagLabel.imy_width + 8));
        }];
    }
    
    // 只有多卡片样式 才会有选择框
    if (self.isSelected != 0) {
        [self.selectView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.mas_trailing).offset(-6);
            make.width.height.equalTo(@14);
            make.top.equalTo(@9);
        }];
        
        // 扣除 选择框+间距
        titleMaxWidth -= (14 + 6);
    }
    
    // 设置标题最长宽度
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
       make.width.lessThanOrEqualTo(@(titleMaxWidth));
    }];
}

- (void)imy_timerRuning {
    if (!self.giftItem.hasCountdown) {
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
        return;
    }
    NSInteger const countdown = self.giftItem.countdown_seconds;
    NSInteger const hour = MIN(99, countdown / 3600);
    NSInteger const minutes = MIN(59, (countdown % 3600) / 60);
    NSInteger const second = countdown % 60;
    
    self.tagLabel.text = [NSString stringWithFormat:@"%.2ld:%.2ld:%.2ld", hour, minutes, second];
}

- (void)onExplainViewClick {
    if (self.giftItem.desc_uri.length > 0) {
        // 赠礼说明跳转协议
        [[IMYURIManager sharedInstance] runActionWithString:self.giftItem.desc_uri];
    } else {
        // 赠礼说明弹窗
        IMYSGVipGiftExplainDialog *dialog = [IMYSGVipGiftExplainDialog new];
        dialog.giftItem = self.giftItem;
        [dialog show];
    }
}

#pragma mark - 属性

- (UIImageView *)iconView {
    if (!_iconView) {
        _iconView = [[UIImageView alloc] init];
        _iconView.imy_size = CGSizeMake(38, 38);
        _iconView.layer.cornerRadius = 8;
        _iconView.layer.masksToBounds = YES;
        _iconView.layer.borderColor = IMY_COLOR_KEY(kCK_Black_E).CGColor;
        _iconView.layer.borderWidth = (SCREEN_SCALE > 2 ? 2 / SCREEN_SCALE : 0.5);
        _iconView.hidden = YES;
        [self addSubview:_iconView];
    }
    return _iconView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        [self addSubview:_titleLabel];
    }
    return _titleLabel;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] init];
        _priceLabel.hidden = YES;
        [self addSubview:_priceLabel];
    }
    return _priceLabel;
}

- (UILabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [[UILabel alloc] init];
        _tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
        _tagLabel.backgroundColor = [IMY_COLOR_KEY(@"#FF4D88") colorWithAlphaComponent:0.1];
        _tagLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        [_tagLabel imy_drawAllCornerRadius:4];
        _tagLabel.textAlignment = NSTextAlignmentCenter;
        _tagLabel.hidden = YES;
        [self addSubview:_tagLabel];
    }
    return _tagLabel;
}

- (UILabel *)preTagLabel {
    if (!_preTagLabel) {
        _preTagLabel = [[UILabel alloc] init];
        _preTagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        [_preTagLabel imy_setTextColor:kCK_Black_M];
        _preTagLabel.text = @"限时";
        _preTagLabel.hidden = YES;
        [self addSubview:_preTagLabel];
    }
    return _preTagLabel;
}

- (UIImageView *)explainView {
    if (!_explainView) {
        _explainView = [[IMYTouchEXImageView alloc] init];
        _explainView.imy_size = CGSizeMake(12, 12);
        _explainView.image = [UIImage imageNamed:@"icon_gift_info_help"];
        _explainView.userInteractionEnabled = YES;
        _explainView.hidden = YES;
        [self addSubview:_explainView];
        
        @weakify(self);
        [_explainView bk_whenTapped:^{
            @strongify(self);
            [self onExplainViewClick];
        }];
    }
    return _explainView;
}

- (UIImageView *)selectView {
    if (!_selectView) {
        _selectView = [[UIImageView alloc] init];
        _selectView.imy_size = CGSizeMake(14, 14);
        _selectView.hidden = YES;
        [self addSubview:_selectView];
    }
    return _selectView;
}

@end
