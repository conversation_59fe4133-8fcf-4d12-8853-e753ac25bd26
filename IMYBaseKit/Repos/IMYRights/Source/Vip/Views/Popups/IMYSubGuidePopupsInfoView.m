//
//  IMYSubGuidePopupsInfoView.m
//  IMYBaseKit
//
//  Created by ljh on 2024/12/2.
//

#import "IMYSubGuidePopupsInfoView.h"
#import "IMYSubGuideManager.h"
#import "IMYSGAPopupsViewV1.h"
#import "IMYSGAPopupsViewV2.h"
#import "IMYSGAPopupsViewV3.h"
#import "IMYSGAPopupsViewV4.h"
#import "IMYSGAPopupsViewV5.h"

@interface IMYSubGuidePopupsInfoView ()

@property (nonatomic, copy) NSArray<NSDictionary *> *windowsData;
@property (nonatomic, copy) NSArray<NSDictionary *> *globalFrequency;
@property (nonatomic, assign) BOOL isRunFinished;

/// 显示的弹窗数据
@property (nonatomic, copy) NSDictionary *popupInfo;
@property (nonatomic, copy) NSArray<NSDictionary *> *popupValidList;
@property (nonatomic, assign) NSInteger popupCountdown;
@property (nonatomic, assign) NSInteger popupPricingId;

@property (nonatomic, strong) UIView<IMYSGAPopupsViewProtocol> *realPopupsView;

/// 触发场景：1：进入支付页 2：退出支付页 3：取消支付
@property (nonatomic, assign) IMYSGRPopupsTriggerScene userTriggerScene;

@end

@implementation IMYSubGuidePopupsInfoView

IMY_KYLIN_FUNC_IDLE_ASYNC {
    [IMYSubGuidePopupsInfoView cleanPopupHistorys];
}

+ (void)cleanPopupHistorys {
    IMYKV *kv = [IMYKV defaultKV];
    NSArray *allKeys = kv.allKeys;
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    for (NSString *key in allKeys) {
        if (![key hasPrefix:@"vip-popup-"]) {
            continue;
        }
        NSArray<NSNumber *> *showedArray = [kv arrayForKey:key];
        if (showedArray.count > 0) {
            NSInteger diff = nowTime - showedArray.lastObject.integerValue;
            if (diff > 86400 * 180) {
                // 最后一次曝光超过180天，则删除这条记录
                [kv removeForKey:key];
            }
        }
    }
}

#pragma mark - 逻辑

- (void)startLoading {
    // 审核状态下无需弹窗
    if (IMYRightsSDK.isSubAuditReview) {
        return;
    }
    // 已经初始化过
    if (self.isRunFinished) {
        NSAssert(NO, @"已初始化过!");
        return;
    }
    // 无价格包判断Block
    if (!self.onShouldConfirmBlock) {
        NSAssert(NO, @"无价格包校验Block");
        return;
    }
    // 无价格包类型判断Block
    if (!self.onShouldSubTypeConfirmBlock) {
        NSAssert(NO, @"无价格包类型校验Block");
        return;
    }
    @weakify(self);
    self.userTriggerScene = IMYSGRPopupsTriggerScenePaymentIn;
    [self requestPopupsInfosWithCompleted:^(NSArray<NSDictionary *> *windowsInfo, NSArray<NSDictionary *> *globalFrequency) {
        @strongify(self);
        if (!self) {
            return;
        }
        [self setupWithPopupsInfos:windowsInfo globalFrequency:globalFrequency];
    }];
}

- (void)requestPopupsInfosWithCompleted:(void(^)(NSArray<NSDictionary *> *, NSArray<NSDictionary *> *))completedBlock  {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"scene_key"] = self.session.sceneKey ?: @"";
    if (self.pageEntryType == 2) {
        params[@"page_key"] = @"PAY_WINDOW";
    } else if (self.pageEntryType == 1) {
        params[@"page_key"] = @"PAY";
    }
    [[[IMYServerRequest getPath:@"v3/popups" host:sub_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        // 弹窗数据
        NSArray<NSDictionary *> *windowsInfo = x.responseObject[@"windows"];
        NSArray<NSDictionary *> *validDatas = [[windowsInfo bk_select:^BOOL(NSDictionary *item) {
            return [item[@"type"] integerValue] == 1;
        }] copy];
        
        // 全局弹窗频控
        NSArray<NSDictionary *> *frequencySettings = x.responseObject[@"frequency_setting"];
        NSArray<NSDictionary *> *globalFrequency = [[frequencySettings bk_select:^BOOL(NSDictionary *item) {
            return YES;
        }] copy];
        
        completedBlock(validDatas, globalFrequency);
    } error:^(NSError *error) {
        completedBlock(nil, nil);
    }];
}

- (void)setupWithPopupsInfos:(NSArray<NSDictionary *> *)windowsInfo globalFrequency:(NSArray<NSDictionary *> *)globalFrequency {
    self.windowsData = windowsInfo;
    self.globalFrequency = globalFrequency;
    self.isRunFinished = YES;
    if (!self.windowsData.count) {
        if (self.onConfirmCompletedBlock) {
            self.onConfirmCompletedBlock(NO);
        }
        return;
    }
    // 以服务端返回的顺序遍历
    @weakify(self);
    [self foreachPopupInfoWithIndex:0 onlyPricingId:0 windowsData:self.windowsData completed:^(BOOL canShow) {
        @strongify(self);
        if (self.onConfirmCompletedBlock) {
            self.onConfirmCompletedBlock(canShow);
        }
    }];
}

- (BOOL)shouldShowWithGlobalFrequencyLimit:(IMYSGRPopupsTriggerScene const)userTriggerScene {
    BOOL hasNeedShow = NO;
    // 全局弹窗Key
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-global-%ld", [IMYPublicAppHelper shareAppHelper].userid, userTriggerScene];
    NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    
    // 曝光过滤条件
    NSDictionary * const exposeConfig = [self.globalFrequency bk_match:^BOOL(NSDictionary *config) {
        IMYSGRPopupsTriggerScene const trigger_scene = [config[@"trigger_scene"] integerValue];
        return trigger_scene == userTriggerScene;
    }];
    NSInteger const limitDays = [exposeConfig[@"days"] integerValue];
    NSInteger const limitTimes = [exposeConfig[@"times"] integerValue];
    NSInteger const limitHours = [exposeConfig[@"interval_hours"] integerValue];
    
    // 频次判断
    do {
        // 服务端不限制频控，每次进入必弹
        if (limitDays == 0 && limitTimes == 0 && limitHours == 0) {
            hasNeedShow = YES;
            break;
        }
        // 间隔时间
        if (limitHours > 0 && showedArray.count > 0) {
            NSInteger const lastDiffTime = nowTime - showedArray.lastObject.integerValue;
            NSInteger const diffHours = lastDiffTime / 3600;
            if (limitHours > diffHours) {
                break;
            }
        }
        // 为 0 则为无限弹
        if (limitTimes == 0) {
            hasNeedShow = YES;
            break;
        }
        // 多少天内可以弹几次
        NSInteger const beforeTime = nowTime - 86400 * limitDays;
        NSInteger showedCount = 0;
        for (NSNumber *time in showedArray) {
            if (time.integerValue > beforeTime) {
                showedCount += 1;
            }
        }
        hasNeedShow = (showedCount < limitTimes);
    } while (0);
    
    return hasNeedShow;
}

- (BOOL)shouldShowWithPopupInfo:(NSDictionary *)popupInfo {
    BOOL hasNeedShow = NO;
    // 单个弹窗的频控: 获取历史曝光数据，判断时间周期内弹了几次
    NSInteger const popupId = [popupInfo[@"id"] integerValue];
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
    NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    
    // 曝光过滤条件
    NSDictionary *exposeConfig = popupInfo[@"promotion"][@"expose_config"];
    NSInteger const limitDays = [exposeConfig[@"days"] integerValue];
    NSInteger const limitTimes = [exposeConfig[@"times"] integerValue];
    NSInteger const limitHours = [exposeConfig[@"interval_hours"] integerValue];
    
    // 频次判断
    do {
        // 服务端不限制频控，每次进入必弹
        if (limitDays == 0 && limitTimes == 0 && limitHours == 0) {
            hasNeedShow = YES;
            break;
        }
        // 间隔时间
        if (limitHours > 0 && showedArray.count > 0) {
            NSInteger const lastDiffTime = nowTime - showedArray.lastObject.integerValue;
            NSInteger const diffHours = lastDiffTime / 3600;
            if (limitHours > diffHours) {
                break;
            }
        }
        // 为 0 则为无限弹
        if (limitTimes == 0) {
            hasNeedShow = YES;
            break;
        }
        // 多少天内可以弹几次
        NSInteger const beforeTime = nowTime - 86400 * limitDays;
        NSInteger showedCount = 0;
        for (NSNumber *time in showedArray) {
            if (time.integerValue > beforeTime) {
                showedCount += 1;
            }
        }
        hasNeedShow = (showedCount < limitTimes);
    } while (0);
    
    return hasNeedShow;
}

- (void)saveShowCountWithPopupInfo:(NSDictionary *)popupInfo {
    // 存储弹窗次数
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    
    {
        // 全局弹窗
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-global-%ld", [IMYPublicAppHelper shareAppHelper].userid, self.userTriggerScene];
        NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
        
        NSMutableArray *newShowedArray = [NSMutableArray arrayWithArray:showedArray];
        [newShowedArray addObject:@(nowTime)];
        [[IMYKV defaultKV] setArray:newShowedArray forKey:kvKey];
    }
    
    {
        // 单个弹窗
        NSInteger const popupId = [popupInfo[@"id"] integerValue];
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
        NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
        
        NSMutableArray *newShowedArray = [NSMutableArray arrayWithArray:showedArray];
        [newShowedArray addObject:@(nowTime)];
        [[IMYKV defaultKV] setArray:newShowedArray forKey:kvKey];
    }
}

- (void)canShowPopupInfoWithInfo:(NSDictionary * const)popupInfo
                   onlyPricingId:(NSInteger const)onlyPricingId
                       completed:(void(^ const)(BOOL canShow))completedBlock {
    // 获取弹窗ID
    NSInteger const popupId = [popupInfo[@"id"] integerValue];
    if (popupId == 0) {
        completedBlock(NO);
        return;
    }
    // 触发场景不一致
    IMYSGRPopupsTriggerScene const trigger_scene = [popupInfo[@"trigger_scene"] integerValue];
    if (trigger_scene != self.userTriggerScene) {
        completedBlock(NO);
        return;
    }
    // 判断弹窗归属的价格包是否存在
    BOOL isValidPricingId = YES;
    NSArray *popupValidList = nil;
    NSArray<NSDictionary *> * const tempList = popupInfo[@"promotion"][@"list"];
    NSInteger const show_type = [popupInfo[@"promotion"][@"show_type"] integerValue];
    if (show_type == 6 || show_type == 7) {
        // 赠礼弹窗
        popupValidList = [tempList bk_select:^BOOL(NSDictionary *map) {
            // 只判断单个价格包
            NSInteger const pricing_package_id = [map[@"pricing_package_id"] integerValue];
            if (pricing_package_id > 0) {
                NSInteger const pricingId = pricing_package_id;
                if (onlyPricingId > 0) {
                    // 只判断选中的价格包
                    return onlyPricingId == pricingId;
                } else {
                    return self.onShouldConfirmBlock(pricingId);
                }
            }
            // 价格组
            NSArray<NSNumber *> *pricing_package_ids = map[@"pricing_package_ids"];
            if (pricing_package_ids.count > 0) {
                return [pricing_package_ids bk_any:^BOOL(NSNumber *priceIdNum) {
                    NSInteger const pricingId = [priceIdNum integerValue];
                    if (onlyPricingId > 0) {
                        // 只判断选中的价格包
                        return onlyPricingId == pricingId;
                    } else {
                        return self.onShouldConfirmBlock(pricingId);
                    }
                }];
            }
            // 价格类型
            NSArray<NSNumber *> *sub_types = map[@"sub_types"];
            if (sub_types.count > 0) {
                return [sub_types bk_any:^BOOL(NSNumber *subTypeNum) {
                    NSInteger const sub_type = [subTypeNum integerValue];
                    return self.onShouldSubTypeConfirmBlock(sub_type);
                }];
            }
            
            return NO;
        }];
        // 无效的价格包列表
        if (!popupValidList.count) {
            isValidPricingId = NO;
        }
    } else if (show_type == 5) {
        // 试用弹窗则无需判断价格包是否存在
        popupValidList = tempList;
        // 无效的价格包列表
        if (!popupValidList.count) {
            isValidPricingId = NO;
        }
    } else if (tempList.count > 0) {
        popupValidList = [tempList bk_select:^BOOL(NSDictionary *map) {
            NSInteger const pricingId = [map[@"pricing_package_id"] integerValue];
            if (pricingId > 0) {
                // 只判断单个价格包
                if (onlyPricingId > 0) {
                    return onlyPricingId == pricingId;
                } else { // 包含整个价格组
                    return self.onShouldConfirmBlock(pricingId);
                }
            }
            return NO;
        }];
        // 无效的价格包列表
        if (!popupValidList.count) {
            isValidPricingId = NO;
        }
    } else {
        NSInteger const pricingId = [popupInfo[@"promotion"][@"pricing_package_id"] integerValue];
        BOOL isValidConfig = NO;
        if (pricingId > 0) {
            // 只判断单个价格包
            if (onlyPricingId > 0) {
                isValidConfig = (onlyPricingId == pricingId);
            } else { // 包含整个价格组
                isValidConfig = self.onShouldConfirmBlock(pricingId);
            }
        }
        if (!isValidConfig) {
            isValidPricingId = NO;
        }
    }
    
    // 判断是否处于预览模式下
    BOOL const isDebugPreview = (!self.globalFrequency.count && [IMYKV.defaultKV boolForKey:@"debug/vip/preview"]);
    
    // 价格包ID不匹配，并且不在 Debug Preview 情况下，才不弹窗
    if (!isValidPricingId && !isDebugPreview) {
        completedBlock(NO);
        return;
    }
    
    // 修复预览模式下的完整价格包卡片赋值
    if (!popupValidList.count && isDebugPreview) {
        popupValidList = tempList;
    }
    
    // 全局频控
    BOOL globalCanShow = [self shouldShowWithGlobalFrequencyLimit:self.userTriggerScene];
    // 单个弹窗的频控
    BOOL popupCanShow = [self shouldShowWithPopupInfo:popupInfo];

#ifdef DEBUG
    // 小火箭忽略频控
    BOOL const isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        globalCanShow = YES;
        popupCanShow = YES;
    }
#endif
    
    if (globalCanShow && popupCanShow) {
        // 在显示范围内，请求接口确认是否可以弹窗
        @weakify(self);
        [self requestPopupsConfirmWithID:popupId completed:^(BOOL canShow, NSInteger countdown, NSInteger pricingId) {
            @strongify(self);
            if (!self) {
                return;
            }
            // 流程已切换，忽略之前的请求结果
            if (trigger_scene != self.userTriggerScene) {
                return;
            }
            if (canShow) {
                self.popupInfo = popupInfo;
                self.popupValidList = popupValidList;
                if (isDebugPreview && !isValidPricingId) {
                    // 预览模式下，无有效价格包ID，则使用当前价格包id
                    self.popupPricingId = self.currentPricing.id;
                } else {
                    self.popupPricingId = self.onShouldConfirmBlock(pricingId) ? pricingId : 0;
                }
                [self setupWithCountdownTime:countdown];
                // 回调外部领劵成功
                completedBlock(YES);
            } else {
                completedBlock(NO);
            }
        }];
    } else {
        // 超出显示次数
        completedBlock(NO);
    }
}

- (void)foreachPopupInfoWithIndex:(NSInteger const)index
                    onlyPricingId:(NSInteger const)onlyPricingId
                      windowsData:(NSArray<NSDictionary *> * const)windowsData
                        completed:(void(^)(BOOL canShow))completedBlock {
    NSDictionary * const popupInfo = (index < windowsData.count ? windowsData[index] : nil);
    if (!popupInfo) {
        // 已经无数据
        completedBlock(NO);
        return;
    }
    @weakify(self);
    [self canShowPopupInfoWithInfo:popupInfo onlyPricingId:onlyPricingId completed:^(BOOL canShow) {
        @strongify(self);
        if (!self) {
            return;
        }
        if (canShow) {
            // 回调外部领劵成功
            completedBlock(YES);
        } else {
            // 下一个弹窗
            [self foreachPopupInfoWithIndex:index + 1 onlyPricingId:onlyPricingId windowsData:windowsData completed:completedBlock];
        }
    }];
}

- (BOOL)hasPopupsInfoWithTriggerScene:(const IMYSGRPopupsTriggerScene)userTriggerScene {
    // 遍历所有弹窗数据
    BOOL hasPopupsInfo = NO;
    for (NSDictionary *popupInfo in self.windowsData) {
        // 获取弹窗ID
        NSInteger const popupId = [popupInfo[@"id"] integerValue];
        if (popupId == 0) {
            continue;
        }
        // 触发场景不一致
        IMYSGRPopupsTriggerScene const trigger_scene = [popupInfo[@"trigger_scene"] integerValue];
        if (trigger_scene != userTriggerScene) {
            continue;
        }
        // 判断弹窗归属的价格包是否存在
        BOOL isValidPricingId = YES;
        NSArray *popupValidList = nil;
        NSArray<NSDictionary *> * const tempList = popupInfo[@"promotion"][@"list"];
        NSInteger const show_type = [popupInfo[@"promotion"][@"show_type"] integerValue];
        if (show_type == 6 || show_type == 7) {
            // 赠礼弹窗
            popupValidList = [tempList bk_select:^BOOL(NSDictionary *map) {
                // 只判断单个价格包
                NSInteger const pricing_package_id = [map[@"pricing_package_id"] integerValue];
                if (pricing_package_id > 0) {
                    NSInteger const pricingId = pricing_package_id;
                    return self.onShouldConfirmBlock(pricingId);
                }
                // 价格组
                NSArray<NSNumber *> *pricing_package_ids = map[@"pricing_package_ids"];
                if (pricing_package_ids.count > 0) {
                    return [pricing_package_ids bk_any:^BOOL(NSNumber *priceIdNum) {
                        NSInteger const pricingId = [priceIdNum integerValue];
                        return self.onShouldConfirmBlock(pricingId);
                    }];
                }
                // 价格类型
                NSArray<NSNumber *> *sub_types = map[@"sub_types"];
                if (sub_types.count > 0) {
                    return [sub_types bk_any:^BOOL(NSNumber *subTypeNum) {
                        NSInteger const sub_type = [subTypeNum integerValue];
                        return self.onShouldSubTypeConfirmBlock(sub_type);
                    }];
                }
                
                return NO;
            }];
            // 无效的价格包列表
            if (!popupValidList.count) {
                isValidPricingId = NO;
            }
        } else if (show_type == 5) {
            // 试用弹窗则无需判断价格包是否存在
            popupValidList = tempList;
            // 无效的价格包列表
            if (!popupValidList.count) {
                isValidPricingId = NO;
            }
        } else if (tempList.count > 0) {
            popupValidList = [tempList bk_select:^BOOL(NSDictionary *map) {
                NSInteger const pricingId = [map[@"pricing_package_id"] integerValue];
                if (pricingId > 0) {
                    // 包含整个价格组
                    return self.onShouldConfirmBlock(pricingId);
                }
                return NO;
            }];
            // 无效的价格包列表
            if (!popupValidList.count) {
                isValidPricingId = NO;
            }
        } else {
            NSInteger const pricingId = [popupInfo[@"promotion"][@"pricing_package_id"] integerValue];
            BOOL isValidConfig = NO;
            if (pricingId > 0) {
                // 包含整个价格组
                isValidConfig = self.onShouldConfirmBlock(pricingId);
            }
            if (!isValidConfig) {
                isValidPricingId = NO;
            }
        }
        // 判断是否处于预览模式下
        BOOL const isDebugPreview = (!self.globalFrequency.count && [IMYKV.defaultKV boolForKey:@"debug/vip/preview"]);
        
        // 价格包ID不匹配，并且不在 Debug Preview 情况下，才不弹窗
        if (!isValidPricingId && !isDebugPreview) {
            continue;
        }
        
        // 全局频控
        BOOL globalCanShow = [self shouldShowWithGlobalFrequencyLimit:userTriggerScene];
        // 单个弹窗的频控
        BOOL popupCanShow = [self shouldShowWithPopupInfo:popupInfo];
        
#ifdef DEBUG
        // 小火箭忽略频控
        BOOL const isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
        if (isIgnore) {
            globalCanShow = YES;
            popupCanShow = YES;
        }
#endif
        // 满足全局频控和个人频控，可能可以弹
        if (globalCanShow && popupCanShow) {
            hasPopupsInfo = YES;
            break;
        }
    }
    
    return hasPopupsInfo;
}

- (void)payCancelledAction:(void (^)(BOOL))completedBlock {
    // 在显示范围内，请求接口确认是否可以弹窗
    self.userTriggerScene = IMYSGRPopupsTriggerScenePaymentCancel;
    
    // 优先取当前锚定价格包的优惠弹窗，当前锚定无优惠弹窗，才取首个优惠弹窗
    NSInteger const onlyPricingId = self.currentPricing.id;
    // 以服务端返回的顺序遍历，有超时控制
    __block BOOL isCallbacked = NO;
    @weakify(self);
    [self foreachPopupInfoWithIndex:0 onlyPricingId:onlyPricingId windowsData:self.windowsData completed:^(BOOL canShow) {
        @strongify(self);
        if (isCallbacked) {
            return;
        }
        if (!canShow) {
            [self foreachPopupInfoWithIndex:0 onlyPricingId:0 windowsData:self.windowsData completed:^(BOOL canShow) {
                @strongify(self);
                if (isCallbacked) {
                    return;
                }
                // 回调外部领劵结果
                isCallbacked = YES;
                completedBlock(canShow);
            }];
        } else {
            // 回调外部领劵结果
            isCallbacked = YES;
            completedBlock(canShow);
        }
    }];
    
    // 超时需要继续返回
    imy_asyncMainBlock(self.pageGoBackWaitTime, ^{
        @strongify(self);
        if (isCallbacked) {
            return;
        }
        isCallbacked = YES;
        completedBlock(NO);
    });
}

- (void)pageGoBackAction:(void (^)(BOOL))completedBlock {
    // 在显示范围内，请求接口确认是否可以弹窗
    self.userTriggerScene = IMYSGRPopupsTriggerScenePaymentOut;
    
    // 以服务端返回的顺序遍历，有超时控制
    __block BOOL isCallbacked = NO;
    @weakify(self);
    [self foreachPopupInfoWithIndex:0 onlyPricingId:0 windowsData:self.windowsData completed:^(BOOL canShow) {
        @strongify(self);
        if (isCallbacked) {
            return;
        }
        // 回调外部领劵结果
        isCallbacked = YES;
        completedBlock(canShow);
    }];
    
    // 超时需要继续返回
    imy_asyncMainBlock(self.pageGoBackWaitTime, ^{
        @strongify(self);
        if (isCallbacked) {
            return;
        }
        isCallbacked = YES;
        completedBlock(NO);
    });
}

- (NSArray<NSNumber *> *)popupValidPriceIds {
    NSMutableArray *popupValidPriceIds = [NSMutableArray array];
    [self.popupValidList bk_each:^(NSDictionary *map) {
        NSInteger const pricing_package_id = [map[@"pricing_package_id"] integerValue];
        if (pricing_package_id > 0) {
            [popupValidPriceIds addObject:@(pricing_package_id)];
            return;
        }
        // 价格组
        NSArray<NSNumber *> *pricing_package_ids = map[@"pricing_package_ids"];
        if (pricing_package_ids.count > 0) {
            [popupValidPriceIds addObjectsFromArray:pricing_package_ids];
            return;
        }
        NSArray<NSNumber *> *sub_types = map[@"sub_types"];
        if (sub_types.count > 0) {
            [popupValidPriceIds addObject:@(self.currentPricing.id)];
            return;
        }
    }];
    return [popupValidPriceIds copy];
}

- (CGFloat)pageGoBackWaitTime {
    return 0.6;
}

- (void)requestPopupsConfirmWithID:(NSInteger const)popupId
                         completed:(void(^)(BOOL canShow, NSInteger countdown, NSInteger pricingId))completedBlock {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"scene_key"] = self.session.sceneKey ?: @"";
    params[@"id"] = @(popupId);
    
    [[[IMYServerRequest postPath:@"v3/popups/confirm" host:sub_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        BOOL canShow = ([x.responseObject[@"show"] integerValue] == 1);
        if (canShow) {
            NSInteger countdown = [x.responseObject[@"countdown_seconds"] integerValue];
            NSInteger pricingId = [x.responseObject[@"pricing_package_id"] integerValue];
            completedBlock(YES, countdown, pricingId);
        } else {
            completedBlock(NO, 0, 0);
        }
    } error:^(NSError *error) {
        completedBlock(NO, 0, 0);
    }];
}

#pragma mark - UI

- (void)show {
    if (self.superview) {
        return;
    }
    self.hidden = NO;
    
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    self.frame = window.bounds;
    [window addSubview:self];
    
    NSInteger const show_type = [self.popupInfo[@"promotion"][@"show_type"] integerValue];
    if (show_type == 6 || show_type == 7) {
        self.realPopupsView = [IMYSGAPopupsViewV5 new];
    } else if (show_type == 5) {
        self.realPopupsView = [IMYSGAPopupsViewV4 new];
    } else if (show_type == 4) {
        self.realPopupsView = [IMYSGAPopupsViewV3 new];
    } else if (show_type == 2 || show_type == 3) {
        self.realPopupsView = [IMYSGAPopupsViewV2 new];
    } else {
        self.realPopupsView = [IMYSGAPopupsViewV1 new];
    }
    self.realPopupsView.frame = self.bounds;
    [self addSubview:self.realPopupsView];
    
    // 弹窗配置
    {
        self.realPopupsView.session = self.session;
        self.realPopupsView.currentPricing = self.currentPricing;
        self.realPopupsView.currentPriceCell = self.currentPriceCell;
        self.realPopupsView.currentPriceBoxFrame = self.currentPriceBoxFrame;
        self.realPopupsView.popupInfo = self.popupInfo;
        self.realPopupsView.popupPricingId = self.popupPricingId;
        self.realPopupsView.popupValidList = self.popupValidList;
        self.realPopupsView.userTriggerScene = self.userTriggerScene;
        @weakify(self);
        self.realPopupsView.onTimeChangeBlock = ^NSInteger{
            @strongify(self);
            return self.popupCountdown;
        };
        self.realPopupsView.onDismissedBlock = ^(NSInteger actionType) {
            @strongify(self);
            [self onRealPopupsViewDismiss:actionType];
        };
    }
    
    // 真正显示弹窗
    [self.realPopupsView show];
    
    // 弹窗显示，存储显示次数
    [self saveShowCountWithPopupInfo:self.popupInfo];
}

- (void)dismiss {
    [self.realPopupsView dismiss];
}

- (BOOL)isShowing {
    return (self.realPopupsView && self.superview && !self.hidden);
}

- (void)updatePricingToAlertView {
    self.realPopupsView.session = self.session;
    self.realPopupsView.currentPricing = self.currentPricing;
    self.realPopupsView.currentPriceCell = self.currentPriceCell;
    self.realPopupsView.currentPriceBoxFrame = self.currentPriceBoxFrame;
}

- (void)onRealPopupsViewDismiss:(NSInteger const)actionType {
    if (self.hidden) {
        return;
    }
    self.hidden = YES;
    [self removeFromSuperview];
    self.realPopupsView = nil;
    self.currentPriceCell = nil;
    // 回调给业务方
    if (self.onDismissedBlock) {
        self.onDismissedBlock(actionType);
    }
}

#pragma mark - 定时器

- (void)setupWithCountdownTime:(NSInteger const)popupCountdown {
    _popupCountdown = MAX(0, popupCountdown);
    if (_popupCountdown > 0) {
        [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    } else {
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    }
}

- (void)imy_timerRuning {
    _popupCountdown -= 1;
    if (_popupCountdown <= 0) {
        _popupCountdown = 0;
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    }
}

@end
