//
//  IMYSubGuidePopupsInfoView.h
//  IMYBaseKit
//
//  Created by ljh on 2024/12/2.
//

#import <UIKit/UIKit.h>
#import "IMYSubGuideSession.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideVipPriceCell.h"

NS_ASSUME_NONNULL_BEGIN
 
@interface IMYSubGuidePopupsInfoView : UIView

/// 对应配置
@property (nonatomic, strong) IMYSubGuideSession *session;
@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, weak) IMYSubGuideVipPriceCell *currentPriceCell;
/// 价格包位置
@property (nonatomic, assign) CGRect currentPriceBoxFrame;

/// 页面入口类型：1：支付转化页，2：支付半弹窗
@property (nonatomic, assign) NSInteger pageEntryType;

/// 领劵成功回调，无劵则返回 NO
@property (nonatomic, copy) void(^onConfirmCompletedBlock)(BOOL const hasPopupInfo);
/// 弹窗关闭回调，0：确认按钮，1：关闭按钮，2：离开按钮
@property (nonatomic, copy) void(^onDismissedBlock)(NSInteger const actionType);
/// 弹窗价格包确认
@property (nonatomic, copy) BOOL(^onShouldConfirmBlock)(NSInteger const popupPricingId);
/// 弹窗价格包类型确认
@property (nonatomic, copy) BOOL(^onShouldSubTypeConfirmBlock)(NSInteger const popupSubType);

/// 触发场景：1：进入支付页 2：退出支付页 3：取消支付
@property (nonatomic, assign, readonly) IMYSGRPopupsTriggerScene userTriggerScene;

/// 首次进入，先加载全部弹窗数据，领劵成功会调用 onConfirmCompletedBlock
///  业务侧判断 hasShowed ，是否要显示弹窗
- (void)startLoading;

/// 页面返回事件
- (void)pageGoBackAction:(void(^)(BOOL needShow))completedBlock;

/// 支付取消事件
- (void)payCancelledAction:(void (^)(BOOL needShow))completedBlock;

/// 是否有对应触发场景的弹窗数据，触发场景：1：进入支付页 2：退出支付页 3：取消支付
- (BOOL)hasPopupsInfoWithTriggerScene:(IMYSGRPopupsTriggerScene const)userTriggerScene;

/// 优惠卷所属的价格包id
@property (nonatomic, assign, readonly) NSInteger popupPricingId;

/// 优惠列表中有效的价格包id
@property (nonatomic, strong, readonly) NSArray<NSNumber *> *popupValidPriceIds;

- (void)show;
- (void)dismiss;

@property (nonatomic, assign, readonly) BOOL isShowing;
- (void)updatePricingToAlertView;

@end

NS_ASSUME_NONNULL_END
