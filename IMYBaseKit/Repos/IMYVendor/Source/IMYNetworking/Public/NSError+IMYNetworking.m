//
//  NSError+IMYNetworking.m
//  IMYNetworking
//
//  Created by mario on 16/4/15.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import "NSError+IMYNetworking.h"
#import <AFNetworking/AFNetworking.h>

NSString *const IMYNetworkingOriginalURLKey = @"IMYNetworkingOriginalURL";
NSString *const IMYNetworkingErrorKey = @"IMYNetworkingErrorKey";
NSString *const IMYNetworkingUserInfoKey = @"IMYNetworkingUserInfoKey";
NSString *const IMYNetworkingTaskMetricsKey = @"IMYNetworkingTaskMetricsKey";
NSString *const IMYNetworkingUserTokenKey = @"IMYNetworkingUserTokenKey";
NSString *const IMYNetworkingVirtualTokenKey = @"IMYNetworkingVirtualTokenKey";

@implementation NSError (IMYNetworking)

- (NSURL *)imy_originalURL {
    return self.userInfo[IMYNetworkingOriginalURLKey];
}

- (NSInteger)imy_errorCode {
    return [self.userInfo[IMYNetworkingErrorKey] integerValue];
}

- (NSDictionary *)imy_userInfo {
    return self.userInfo[IMYNetworkingUserInfoKey];
}

- (NSString *)imyaf_userToken {
    return self.userInfo[IMYNetworkingUserTokenKey];
}

- (NSString *)imyaf_virtualToken {
    return self.userInfo[IMYNetworkingVirtualTokenKey];
}

- (id<IMYNetworkingTaskMetricsProtocol>)imyaf_taskMetrics {
    return self.userInfo[IMYNetworkingTaskMetricsKey];
}

- (NSURL *)ns_failingURL {
    return self.userInfo[NSURLErrorFailingURLErrorKey];
}

- (NSDictionary *)af_responseData {
    return self.userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey];
}

- (NSHTTPURLResponse *)af_httpResponse {
    return self.userInfo[AFNetworkingOperationFailingURLResponseErrorKey];
}

@end
