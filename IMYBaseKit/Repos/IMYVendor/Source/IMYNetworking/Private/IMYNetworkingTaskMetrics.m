//
//  IMYNetworkingTaskMetrics.m
//  IMYNetworking
//

#import "IMYNetworkingTaskMetrics.h"
#import "IMYFoundation.h"
#import "_PSDConfig.h"
#import "NSObject+IMYPublicDataConvert.h"

@implementation IMYNetworkingTaskMetrics

@synthesize url = _url;
@synthesize tm_pnt_send = _tm_pnt_send;
@synthesize tm_dur_dns = _tm_dur_dns;
@synthesize tm_dur_conn = _tm_dur_conn;
@synthesize tm_dur_ssl = _tm_dur_ssl;
@synthesize tm_dur_firstP = _tm_dur_firstP;
@synthesize tm_dur_end = _tm_dur_end;


- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics API_AVAILABLE(ios(10.0)) {
    self = [super init];
    if (self) {
        if (metrics.transactionMetrics.count > 0) {
            NSURLSessionTaskTransactionMetrics *transactionMetrics = metrics.transactionMetrics.lastObject;
            
            if (transactionMetrics.fetchStartDate) {
                _tm_pnt_send = (int64_t)([transactionMetrics.fetchStartDate timeIntervalSince1970] * 1000);
            }
            
            if (transactionMetrics.domainLookupStartDate && transactionMetrics.domainLookupEndDate) {
                _tm_dur_dns = (NSInteger)([transactionMetrics.domainLookupEndDate timeIntervalSinceDate:transactionMetrics.domainLookupStartDate] * 1000);
            }
            
            if (transactionMetrics.connectStartDate && transactionMetrics.connectEndDate) {
                _tm_dur_conn = (NSInteger)([transactionMetrics.connectEndDate timeIntervalSinceDate:transactionMetrics.connectStartDate] * 1000);
            }
            
            if (transactionMetrics.secureConnectionStartDate && transactionMetrics.secureConnectionEndDate) {
                _tm_dur_ssl = (NSInteger)([transactionMetrics.secureConnectionEndDate timeIntervalSinceDate:transactionMetrics.secureConnectionStartDate] * 1000);
            }
            
            if (transactionMetrics.responseStartDate && transactionMetrics.requestEndDate) {
                _tm_dur_firstP = (NSInteger)([transactionMetrics.responseStartDate timeIntervalSinceDate:transactionMetrics.requestEndDate] * 1000);
            }
            
            if (transactionMetrics.responseEndDate && transactionMetrics.fetchStartDate) {
                _tm_dur_end = (NSInteger)([transactionMetrics.responseEndDate timeIntervalSinceDate:transactionMetrics.fetchStartDate] * 1000);
            }
        }
    }
    return self;
}

@end

#pragma mark - IMYNetworkingTaskMetricsManager

@interface IMYNetworkingTaskMetricsManager ()
@property (nonatomic, strong) NSMapTable<NSNumber *, IMYNetworkingTaskMetrics *> *taskMetricsMap;
@property (nonatomic, strong) NSLock *accessLock;
@end

@implementation IMYNetworkingTaskMetricsManager

+ (instancetype)sharedManager {
    static IMYNetworkingTaskMetricsManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 使用NSMapTable存储taskIdentifier到metrics的映射，使用强引用key和value
        _taskMetricsMap = [NSMapTable mapTableWithKeyOptions:NSPointerFunctionsStrongMemory
                                                valueOptions:NSPointerFunctionsStrongMemory];
        _accessLock = [[NSLock alloc] init];
        _accessLock.name = @"IMYNetworkingTaskMetricsManager.accessLock";
    }
    return self;
}

- (void)setTaskMetrics:(IMYNetworkingTaskMetrics *)metrics forTask:(NSURLSessionTask *)task {
    if (!metrics || !task) {
        return;
    }

    NSNumber *taskIdentifier = @(task.taskIdentifier);

    [self.accessLock lock];
    @try {
        [self.taskMetricsMap setObject:metrics forKey:taskIdentifier];
    } @finally {
        [self.accessLock unlock];
    }
}

- (nullable IMYNetworkingTaskMetrics *)taskMetricsForTask:(NSURLSessionTask *)task {
    if (!task) {
        return nil;
    }

    NSNumber *taskIdentifier = @(task.taskIdentifier);
    [self.accessLock lock];
    IMYNetworkingTaskMetrics *metrics = nil;
    @try {
        metrics = [self.taskMetricsMap objectForKey:taskIdentifier];
    } @finally {
        [self.accessLock unlock];
    }

    return metrics;
}

- (void)removeTaskMetricsForTask:(NSURLSessionTask *)task {
    if (!task) {
        return;
    }

    NSNumber *taskIdentifier = @(task.taskIdentifier);
    [self.accessLock lock];
    @try {
        [self.taskMetricsMap removeObjectForKey:taskIdentifier];
    } @finally {
        [self.accessLock unlock];
    }
}

@end
