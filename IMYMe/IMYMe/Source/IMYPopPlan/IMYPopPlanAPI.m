//
//  IMYPopPlanAPI.m
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import "IMYPopPlanAPI.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYPopPlanAPI

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYPopPlanAPI *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - 接口

/// 获取投放计划
- (void)getPlanWithCode:(NSString *)code
              onSuccess:(void (^)(NSDictionary *dict))onSuccess
                onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:code forKey:@"code"];
    
    // ext
    NSMutableDictionary *ext = [[NSMutableDictionary alloc] init];
    NSNumber *stageType = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"chatAI/stageType" params:nil];
    [ext imy_setNonNilObject:stageType forKey:@"stage_type"];
    NSNumber *babyMonth = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/babyMonth" params:nil];
    [ext imy_setNonNilObject:babyMonth forKey:@"baby_month"];
    NSNumber *pregnancyWeek = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/pregnancyWeek" params:nil];
    [ext imy_setNonNilObject:pregnancyWeek forKey:@"pregnancy_week"];
    [parameter imy_setNonNilObject:ext forKey:@"ext"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"dialog/popplan" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// 全局频控策略获取
- (void)getGfcPolicyOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                      onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    RACSignal *signal = [IMYServerRequest postPath:@"gfc/policy" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// 冷启动事件上报
- (void)reportColdStartOnSuccess:(void (^)(void))onSuccess
                         onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:IMYUserBehaviorEventTypeColdStart appId:1 onSuccess:onSuccess onError:onError];
}

/// 热启动事件上报
- (void)reportHotStartOnSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:IMYUserBehaviorEventTypeHotStart appId:1 onSuccess:onSuccess onError:onError];
}

/// 用户行为事件上报（指定应用ID）
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {

    // 参数验证
    if (eventType != IMYUserBehaviorEventTypeColdStart &&
        eventType != IMYUserBehaviorEventTypeHotStart) {
        if (onError) {
            NSError *error = [NSError errorWithDomain:@"IMYPopPlanAPI"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"无效的事件类型"}];
            onError(error);
        }
        return;
    }

    // 构建请求参数
    NSTimeInterval currentTimestamp = [[NSDate date] timeIntervalSince1970] * 1000; // 转换为13位毫秒时间戳
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:@(eventType) forKey:@"event"];
    [parameter imy_setNonNilObject:@((NSInteger)currentTimestamp) forKey:@"unix_timestamp"];
    [parameter imy_setNonNilObject:@(appId) forKey:@"app_id"];

    RACSignal *signal = [IMYServerRequest postPath:@"/event/track" host:mgo_seeyouyima_com params:parameter headers:nil];

    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];

    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess();
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

// MARK: - 转换

/// IMYHTTPResponse 转 NSDictionary 或 error
+ (RACSignal *)responseFlattenMap:(IMYHTTPResponse *)x {
    if ([x.responseObject isKindOfClass:[NSDictionary class]]) {
        return [RACSignal return:x.responseObject];
    } else {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:x.userInfo];
        userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey] = x.responseData;
        userInfo[AFNetworkingOperationFailingURLResponseErrorKey] = x.response;
        userInfo[NSLocalizedDescriptionKey] = @"网络缓慢，请稍后再试";
        return [RACSignal error:[NSError errorWithDomain:@"ChatAI" code:-9 userInfo:userInfo]];
    }
}

@end
