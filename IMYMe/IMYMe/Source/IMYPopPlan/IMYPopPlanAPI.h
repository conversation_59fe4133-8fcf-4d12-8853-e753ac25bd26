//
//  IMYPopPlanAPI.h
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 用户行为事件类型
 */
typedef NS_ENUM(NSInteger, IMYUserBehaviorEventType) {
    IMYUserBehaviorEventTypeColdStart = 1,  ///< 冷启动
    IMYUserBehaviorEventTypeHotStart = 2    ///< 热启动
};

@interface IMYPopPlanAPI : NSObject

+ (instancetype)sharedInstance;

// MARK: - 接口

/// 获取投放计划
- (void)getPlanWithCode:(NSString *)code
              onSuccess:(void (^)(NSDictionary *dict))onSuccess
                onError:(void (^)(NSError *error))onError;

/// 全局频控策略获取
- (void)getGfcPolicyOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                      onError:(void (^)(NSError *error))onError;


/// 用户行为事件上报（指定应用ID）
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError;

@end

NS_ASSUME_NONNULL_END
