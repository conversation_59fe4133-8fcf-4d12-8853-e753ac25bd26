//
//  IMYNetworkingTaskMetrics.h
//  IMYNetworking
//
//  Created by <PERSON><PERSON><PERSON> on 2025/9/5.
//

#import <Foundation/Foundation.h>
#import "IMYHTTPInternal.h"

NS_ASSUME_NONNULL_BEGIN

API_AVAILABLE(ios(10.0))
@interface IMYNetworkingTaskMetrics : NSObject <IMYNetworkingTaskMetricsProtocol>

- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics;

@property (nonatomic, strong, readonly) NSURLSessionTaskMetrics *originalMetrics;

@property (nonatomic, copy, readonly) NSString *url;
@property (nonatomic, assign, readonly) int64_t tm_pnt_send;
@property (nonatomic, assign, readonly) NSInteger tm_dur_dns;
@property (nonatomic, assign, readonly) NSInteger tm_dur_conn;
@property (nonatomic, assign, readonly) NSInteger tm_dur_ssl;
@property (nonatomic, assign, readonly) NSInteger tm_dur_firstP;
@property (nonatomic, assign, readonly) NSInteger tm_dur_end;
@property (nonatomic, assign, readonly) NSInteger status_code;
@property (nonatomic, assign, readonly) NSInteger err_code;
@property (nonatomic, assign, readonly) NSInteger net_type;
@property (nonatomic, copy, readonly) NSString *content_type;
@property (nonatomic, assign, readonly) NSInteger recv_bytes;
@property (nonatomic, assign, readonly) NSInteger sent_bytes;
@property (nonatomic, assign, readonly) NSInteger content_length;
@property (nonatomic, copy, readonly) NSString *ip;
@property (nonatomic, assign, readonly) NSInteger method;
@property (nonatomic, copy, readonly) NSString *carrier;

@end

NS_ASSUME_NONNULL_END
