//
//  IMYNetworkingTaskMetrics.m
//  IMYNetworking
//
//  Created by <PERSON><PERSON><PERSON> on 2025/9/5.
//

#import "IMYNetworkingTaskMetrics.h"
#import <AFNetworking/AFNetworkReachabilityManager.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <CoreTelephony/CTCarrier.h>

@implementation IMYNetworkingTaskMetrics

@synthesize url = _url;
@synthesize tm_pnt_send = _tm_pnt_send;
@synthesize tm_dur_dns = _tm_dur_dns;
@synthesize tm_dur_conn = _tm_dur_conn;
@synthesize tm_dur_ssl = _tm_dur_ssl;
@synthesize tm_dur_firstP = _tm_dur_firstP;
@synthesize tm_dur_end = _tm_dur_end;
@synthesize status_code = _status_code;
@synthesize err_code = _err_code;
@synthesize net_type = _net_type;
@synthesize content_type = _content_type;
@synthesize recv_bytes = _recv_bytes;
@synthesize sent_bytes = _sent_bytes;
@synthesize content_length = _content_length;
@synthesize ip = _ip;
@synthesize method = _method;
@synthesize carrier = _carrier;

- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics {
    if (self = [super init]) {
        _originalMetrics = metrics;
        [self parseMetrics];
    }
    return self;
}

- (void)parseMetrics {
    if (@available(iOS 10.0, *)) {
        NSURLSessionTaskTransactionMetrics *metric = self.originalMetrics.transactionMetrics.lastObject;
        if (!metric) return;

        _url = metric.request.URL.absoluteString;
        
        // Timestamps and Durations
        if (metric.requestStartDate) {
            _tm_pnt_send = (int64_t)([metric.requestStartDate timeIntervalSince1970] * 1000);
        }
        
        if (metric.domainLookupStartDate && metric.domainLookupEndDate) {
            _tm_dur_dns = (NSInteger)([metric.domainLookupEndDate timeIntervalSinceDate:metric.domainLookupStartDate] * 1000);
        }
        
        if (metric.connectStartDate && metric.connectEndDate) {
            _tm_dur_conn = (NSInteger)([metric.connectEndDate timeIntervalSinceDate:metric.connectStartDate] * 1000);
        }
        
        if (metric.secureConnectionStartDate && metric.secureConnectionEndDate) {
            _tm_dur_ssl = (NSInteger)([metric.secureConnectionEndDate timeIntervalSinceDate:metric.secureConnectionStartDate] * 1000);
        }
        
        if (metric.responseStartDate && metric.requestEndDate) {
            _tm_dur_firstP = (NSInteger)([metric.responseStartDate timeIntervalSinceDate:metric.requestEndDate] * 1000);
        }
        
        if (self.originalMetrics.taskInterval.start && self.originalMetrics.taskInterval.end) {
            _tm_dur_end = (NSInteger)([self.originalMetrics.taskInterval.end timeIntervalSinceDate:self.originalMetrics.taskInterval.start] * 1000);
        }

        // Request and Response Info
        if ([metric.response isKindOfClass:[NSHTTPURLResponse class]]) {
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)metric.response;
            _status_code = httpResponse.statusCode;
            _content_type = httpResponse.allHeaderFields[@"Content-Type"];
            _content_length = [httpResponse.allHeaderFields[@"Content-Length"] integerValue];
        }
        
        _recv_bytes = metric.countOfResponseBodyBytesReceived;
        _sent_bytes = metric.countOfRequestBodyBytesSent;
        _ip = metric.remoteAddress;
        _method = [self httpMethodFromString:metric.request.HTTPMethod];

        // Network Info
        _net_type = [self currentNetworkType];
        _carrier = [self currentCarrierName];
    }
}

- (NSInteger)httpMethodFromString:(NSString *)method {
    if ([method isEqualToString:@"GET"]) return 1;
    if ([method isEqualToString:@"POST"]) return 2;
    if ([method isEqualToString:@"PUT"]) return 3;
    if ([method isEqualToString:@"DELETE"]) return 4;
    if ([method isEqualToString:@"HEAD"]) return 5;
    if ([method isEqualToString:@"OPTIONS"]) return 6;
    if ([method isEqualToString:@"PATCH"]) return 7;
    return 0; // Unknown
}

- (NSInteger)currentNetworkType {
    AFNetworkReachabilityStatus status = [AFNetworkReachabilityManager sharedManager].networkReachabilityStatus;
    switch (status) {
        case AFNetworkReachabilityStatusReachableViaWWAN:
            return 2; // 2G/3G/4G/5G
        case AFNetworkReachabilityStatusReachableViaWiFi:
            return 1; // WIFI
        case AFNetworkReachabilityStatusNotReachable:
            return 0; // No Network
        default:
            return -1; // Unknown
    }
}

- (NSString *)currentCarrierName {
    CTTelephonyNetworkInfo *networkInfo = [[CTTelephonyNetworkInfo alloc] init];
    CTCarrier *carrier = networkInfo.subscriberCellularProvider;
    return carrier.carrierName ?: @"";
}

@end
